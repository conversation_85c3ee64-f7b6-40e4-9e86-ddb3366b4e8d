<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>API Reference Guide | Unsplasharp Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="API Reference Guide | Unsplasharp Documentation ">
      
      
      <link rel="icon" href="favicon.ico">
      <link rel="stylesheet" href="public/docfx.min.css">
      <link rel="stylesheet" href="public/main.css">
      <meta name="docfx:navrel" content="toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="">
      
      
      <meta name="docfx:docurl" content="https://github.com/rootasjey/unsplasharp/blob/master/docs/api-reference.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="index.html">
            <img id="logo" class="svg" src="logo.svg" alt="Unsplasharp">
            Unsplasharp
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">

      <div class="content">
        <div class="actionbar">

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="api-reference-guide">API Reference Guide</h1>

<p>This comprehensive guide covers all available methods in the Unsplasharp library with detailed examples and use cases.</p>
<h2 id="table-of-contents">Table of Contents</h2>
<ul>
<li><a href="#client-initialization">Client Initialization</a></li>
<li><a href="#photo-methods">Photo Methods</a></li>
<li><a href="#search-methods">Search Methods</a></li>
<li><a href="#collection-methods">Collection Methods</a></li>
<li><a href="#user-methods">User Methods</a></li>
<li><a href="#statistics-methods">Statistics Methods</a></li>
<li><a href="#method-parameters">Method Parameters</a></li>
<li><a href="#return-types">Return Types</a></li>
</ul>
<h2 id="client-initialization">Client Initialization</h2>
<h3 id="basic-initialization">Basic Initialization</h3>
<pre><code class="lang-csharp">using Unsplasharp;

// Basic client with Application ID only
var client = new UnsplasharpClient(&quot;YOUR_APPLICATION_ID&quot;);

// With optional secret for authenticated requests
var client = new UnsplasharpClient(&quot;YOUR_APPLICATION_ID&quot;, &quot;YOUR_SECRET&quot;);

// With logging support
var logger = loggerFactory.CreateLogger&lt;UnsplasharpClient&gt;();
var client = new UnsplasharpClient(&quot;YOUR_APPLICATION_ID&quot;, logger: logger);

// With IHttpClientFactory (recommended for production)
var client = new UnsplasharpClient(&quot;YOUR_APPLICATION_ID&quot;, 
    logger: logger, 
    httpClientFactory: httpClientFactory);
</code></pre>
<h3 id="dependency-injection-setup">Dependency Injection Setup</h3>
<pre><code class="lang-csharp">// In Program.cs or Startup.cs
services.AddUnsplasharp(&quot;YOUR_APPLICATION_ID&quot;);

// Or with configuration
services.AddUnsplasharp(options =&gt;
{
    options.ApplicationId = &quot;YOUR_APPLICATION_ID&quot;;
    options.Secret = &quot;YOUR_SECRET&quot;;
    options.ConfigureHttpClient = client =&gt;
    {
        client.Timeout = TimeSpan.FromSeconds(60);
    };
});
</code></pre>
<h2 id="photo-methods">Photo Methods</h2>
<h3 id="getphoto--getphotoasync">GetPhoto / GetPhotoAsync</h3>
<p>Retrieve a specific photo by ID with optional custom sizing.</p>
<p><strong>Signatures:</strong></p>
<pre><code class="lang-csharp">Task&lt;Photo?&gt; GetPhoto(string id, CancellationToken cancellationToken = default)
Task&lt;Photo?&gt; GetPhoto(string id, int? width = null, int? height = null, CancellationToken cancellationToken = default)
Task&lt;Photo?&gt; GetPhoto(string id, int? width, int? height, int? cropX, int? cropY, int? cropWidth, int? cropHeight, CancellationToken cancellationToken = default)
Task&lt;Photo&gt; GetPhotoAsync(string id, CancellationToken cancellationToken = default) // Throws exceptions
</code></pre>
<p><strong>Examples:</strong></p>
<pre><code class="lang-csharp">// Basic photo retrieval
var photo = await client.GetPhoto(&quot;qcs09SwNPHY&quot;);
if (photo != null)
{
    Console.WriteLine($&quot;Photo by {photo.User.Name}: {photo.Description}&quot;);
    Console.WriteLine($&quot;Regular size: {photo.Urls.Regular}&quot;);
}

// Custom sizing
var resizedPhoto = await client.GetPhoto(&quot;qcs09SwNPHY&quot;, width: 800, height: 600);
Console.WriteLine($&quot;Custom URL: {resizedPhoto?.Urls.Custom}&quot;);

// Cropped version
var croppedPhoto = await client.GetPhoto(&quot;qcs09SwNPHY&quot;, 
    width: 400, height: 400, 
    cropX: 100, cropY: 100, cropWidth: 200, cropHeight: 200);

// Exception-throwing version (recommended for new code)
try
{
    var photo = await client.GetPhotoAsync(&quot;qcs09SwNPHY&quot;);
    Console.WriteLine($&quot;Photo dimensions: {photo.Width}x{photo.Height}&quot;);
}
catch (UnsplasharpNotFoundException)
{
    Console.WriteLine(&quot;Photo not found&quot;);
}
catch (UnsplasharpException ex)
{
    Console.WriteLine($&quot;API error: {ex.Message}&quot;);
}
</code></pre>
<h3 id="getrandomphoto--getrandomphotoasync">GetRandomPhoto / GetRandomPhotoAsync</h3>
<p>Get one or more random photos with optional filtering.</p>
<p><strong>Signatures:</strong></p>
<pre><code class="lang-csharp">Task&lt;Photo?&gt; GetRandomPhoto(CancellationToken cancellationToken = default)
Task&lt;Photo?&gt; GetRandomPhoto(string collectionId, CancellationToken cancellationToken = default)
Task&lt;Photo?&gt; GetRandomPhoto(string[] collectionIds, CancellationToken cancellationToken = default)
Task&lt;Photo?&gt; GetRandomPhoto(int count, string? query = null, string? username = null, bool featured = false, string? collectionId = null, Orientation orientation = Orientation.All, CancellationToken cancellationToken = default)
Task&lt;Photo&gt; GetRandomPhotoAsync(...) // Exception-throwing versions
</code></pre>
<p><strong>Examples:</strong></p>
<pre><code class="lang-csharp">// Simple random photo
var randomPhoto = await client.GetRandomPhoto();

// Random photo from specific collection
var collectionPhoto = await client.GetRandomPhoto(&quot;499830&quot;);

// Random photo from multiple collections
var multiCollectionPhoto = await client.GetRandomPhoto(new[] { &quot;499830&quot;, &quot;194162&quot; });

// Multiple random photos with filters
var naturePhotos = await client.GetRandomPhoto(
    count: 5,
    query: &quot;nature&quot;,
    featured: true,
    orientation: Orientation.Landscape
);

// Random photo from specific user
var userPhoto = await client.GetRandomPhoto(1, username: &quot;chrisjoelcampbell&quot;);
</code></pre>
<h3 id="listphotos--listphotosasync">ListPhotos / ListPhotosAsync</h3>
<p>Get a paginated list of all photos.</p>
<p><strong>Signatures:</strong></p>
<pre><code class="lang-csharp">Task&lt;List&lt;Photo&gt;&gt; ListPhotos(int page = 1, int perPage = 10, OrderBy orderBy = OrderBy.Latest, CancellationToken cancellationToken = default)
Task&lt;List&lt;Photo&gt;&gt; ListPhotosAsync(...) // Exception-throwing version
</code></pre>
<p><strong>Examples:</strong></p>
<pre><code class="lang-csharp">// Get latest photos (default)
var latestPhotos = await client.ListPhotos(page: 1, perPage: 20);

// Get oldest photos first
var oldestPhotos = await client.ListPhotos(
    page: 1, 
    perPage: 10, 
    orderBy: OrderBy.Oldest
);

// Get popular photos
var popularPhotos = await client.ListPhotos(orderBy: OrderBy.Popular);

// Pagination example
for (int page = 1; page &lt;= 5; page++)
{
    var photos = await client.ListPhotos(page: page, perPage: 30);
    Console.WriteLine($&quot;Page {page}: {photos.Count} photos&quot;);
    
    foreach (var photo in photos)
    {
        Console.WriteLine($&quot;  - {photo.Id}: {photo.Description}&quot;);
    }
}
</code></pre>
<h2 id="search-methods">Search Methods</h2>
<h3 id="searchphotos--searchphotosasync">SearchPhotos / SearchPhotosAsync</h3>
<p>Search for photos by query with advanced filtering options.</p>
<p><strong>Signatures:</strong></p>
<pre><code class="lang-csharp">Task&lt;List&lt;Photo&gt;&gt; SearchPhotos(string query, int page = 1, int perPage = 10, CancellationToken cancellationToken = default)
Task&lt;List&lt;Photo&gt;&gt; SearchPhotos(string query, int page, int perPage, OrderBy orderBy, string? collectionIds = null, string? contentFilter = null, string? color = null, Orientation orientation = Orientation.All, CancellationToken cancellationToken = default)
</code></pre>
<p><strong>Examples:</strong></p>
<pre><code class="lang-csharp">// Basic search
var naturePhotos = await client.SearchPhotos(&quot;nature&quot;);

// Advanced search with filters
var filteredPhotos = await client.SearchPhotos(
    query: &quot;mountain landscape&quot;,
    page: 1,
    perPage: 20,
    orderBy: OrderBy.Relevant,
    color: &quot;blue&quot;,
    orientation: Orientation.Landscape
);

// Search with content filtering
var safePhotos = await client.SearchPhotos(
    query: &quot;beach&quot;,
    page: 1,
    perPage: 15,
    contentFilter: &quot;high&quot;
);

// Check search metadata
Console.WriteLine($&quot;Search query: {client.LastPhotosSearchQuery}&quot;);
Console.WriteLine($&quot;Total results: {client.LastPhotosSearchTotalResults}&quot;);
Console.WriteLine($&quot;Total pages: {client.LastPhotosSearchTotalPages}&quot;);
</code></pre>
<h2 id="collection-methods">Collection Methods</h2>
<h3 id="getcollection--getcollectionasync">GetCollection / GetCollectionAsync</h3>
<p>Retrieve a specific collection by ID.</p>
<p><strong>Signatures:</strong></p>
<pre><code class="lang-csharp">Task&lt;Collection?&gt; GetCollection(string id, CancellationToken cancellationToken = default)
Task&lt;Collection&gt; GetCollectionAsync(string id, CancellationToken cancellationToken = default)
</code></pre>
<p><strong>Examples:</strong></p>
<pre><code class="lang-csharp">// Get collection details
var collection = await client.GetCollection(&quot;499830&quot;);
if (collection != null)
{
    Console.WriteLine($&quot;Collection: {collection.Title}&quot;);
    Console.WriteLine($&quot;Description: {collection.Description}&quot;);
    Console.WriteLine($&quot;Total photos: {collection.TotalPhotos}&quot;);
    Console.WriteLine($&quot;Created by: {collection.User.Name}&quot;);
}

// Exception-throwing version
try
{
    var collection = await client.GetCollectionAsync(&quot;499830&quot;);
    Console.WriteLine($&quot;Collection cover: {collection.CoverPhoto.Urls.Regular}&quot;);
}
catch (UnsplasharpNotFoundException)
{
    Console.WriteLine(&quot;Collection not found&quot;);
}
</code></pre>
<h3 id="listcollections--listcollectionsasync">ListCollections / ListCollectionsAsync</h3>
<p>Get a paginated list of all collections.</p>
<p><strong>Signatures:</strong></p>
<pre><code class="lang-csharp">Task&lt;List&lt;Collection&gt;&gt; ListCollections(int page = 1, int perPage = 10, CancellationToken cancellationToken = default)
Task&lt;List&lt;Collection&gt;&gt; ListCollectionsAsync(...) // Exception-throwing version
</code></pre>
<p><strong>Examples:</strong></p>
<pre><code class="lang-csharp">// Get featured collections
var collections = await client.ListCollections(page: 1, perPage: 20);

foreach (var collection in collections)
{
    Console.WriteLine($&quot;{collection.Title} - {collection.TotalPhotos} photos&quot;);
    Console.WriteLine($&quot;  By: {collection.User.Name}&quot;);
    Console.WriteLine($&quot;  Cover: {collection.CoverPhoto.Urls.Small}&quot;);
}

// Pagination through collections
var allCollections = new List&lt;Collection&gt;();
int page = 1;
List&lt;Collection&gt; pageResults;

do
{
    pageResults = await client.ListCollections(page: page, perPage: 30);
    allCollections.AddRange(pageResults);
    page++;
} while (pageResults.Count == 30); // Continue while getting full pages
</code></pre>
<h3 id="getcollectionphotos--getcollectionphotosasync">GetCollectionPhotos / GetCollectionPhotosAsync</h3>
<p>Get photos from a specific collection.</p>
<p><strong>Signatures:</strong></p>
<pre><code class="lang-csharp">Task&lt;List&lt;Photo&gt;&gt; GetCollectionPhotos(string collectionId, int page = 1, int perPage = 10, CancellationToken cancellationToken = default)
Task&lt;List&lt;Photo&gt;&gt; GetCollectionPhotosAsync(...) // Exception-throwing version
</code></pre>
<p><strong>Examples:</strong></p>
<pre><code class="lang-csharp">// Get photos from a collection
var collectionPhotos = await client.GetCollectionPhotos(&quot;499830&quot;, page: 1, perPage: 25);

Console.WriteLine($&quot;Found {collectionPhotos.Count} photos in collection&quot;);
foreach (var photo in collectionPhotos)
{
    Console.WriteLine($&quot;  {photo.Id}: {photo.Description ?? &quot;No description&quot;}&quot;);
    Console.WriteLine($&quot;    By: {photo.User.Name}&quot;);
    Console.WriteLine($&quot;    Likes: {photo.Likes}, Downloads: {photo.Downloads}&quot;);
}
</code></pre>
<h3 id="searchcollections--searchcollectionsasync">SearchCollections / SearchCollectionsAsync</h3>
<p>Search for collections by query.</p>
<p><strong>Signatures:</strong></p>
<pre><code class="lang-csharp">Task&lt;List&lt;Collection&gt;&gt; SearchCollections(string query, int page = 1, int perPage = 10, CancellationToken cancellationToken = default)
Task&lt;List&lt;Collection&gt;&gt; SearchCollectionsAsync(...) // Exception-throwing version
</code></pre>
<p><strong>Examples:</strong></p>
<pre><code class="lang-csharp">// Search for collections
var travelCollections = await client.SearchCollections(&quot;travel&quot;, page: 1, perPage: 15);

Console.WriteLine($&quot;Found {travelCollections.Count} travel collections&quot;);
foreach (var collection in travelCollections)
{
    Console.WriteLine($&quot;{collection.Title} ({collection.TotalPhotos} photos)&quot;);
    Console.WriteLine($&quot;  {collection.Description}&quot;);
}

// Check search metadata
Console.WriteLine($&quot;Search query: {client.LastCollectionsSearchQuery}&quot;);
Console.WriteLine($&quot;Total results: {client.LastCollectionsSearchTotalResults}&quot;);
Console.WriteLine($&quot;Total pages: {client.LastCollectionsSearchTotalPages}&quot;);
</code></pre>
<h2 id="user-methods">User Methods</h2>
<h3 id="getuser--getuserasync">GetUser / GetUserAsync</h3>
<p>Retrieve a user's profile information.</p>
<p><strong>Signatures:</strong></p>
<pre><code class="lang-csharp">Task&lt;User?&gt; GetUser(string username, CancellationToken cancellationToken = default)
Task&lt;User&gt; GetUserAsync(string username, CancellationToken cancellationToken = default)
</code></pre>
<p><strong>Examples:</strong></p>
<pre><code class="lang-csharp">// Get user profile
var user = await client.GetUser(&quot;chrisjoelcampbell&quot;);
if (user != null)
{
    Console.WriteLine($&quot;Name: {user.Name}&quot;);
    Console.WriteLine($&quot;Username: {user.Username}&quot;);
    Console.WriteLine($&quot;Bio: {user.Bio}&quot;);
    Console.WriteLine($&quot;Location: {user.Location}&quot;);
    Console.WriteLine($&quot;Portfolio: {user.PortfolioUrl}&quot;);
    Console.WriteLine($&quot;Total photos: {user.TotalPhotos}&quot;);
    Console.WriteLine($&quot;Total likes: {user.TotalLikes}&quot;);
    Console.WriteLine($&quot;Total collections: {user.TotalCollections}&quot;);
    Console.WriteLine($&quot;Profile image: {user.ProfileImage.Large}&quot;);
}

// Exception-throwing version
try
{
    var user = await client.GetUserAsync(&quot;nonexistentuser&quot;);
    Console.WriteLine($&quot;User found: {user.Name}&quot;);
}
catch (UnsplasharpNotFoundException)
{
    Console.WriteLine(&quot;User not found&quot;);
}
</code></pre>
<h3 id="getuserphotos--getuserphotosasync">GetUserPhotos / GetUserPhotosAsync</h3>
<p>Get photos uploaded by a specific user.</p>
<p><strong>Signatures:</strong></p>
<pre><code class="lang-csharp">Task&lt;List&lt;Photo&gt;&gt; GetUserPhotos(string username, int page = 1, int perPage = 10, OrderBy orderBy = OrderBy.Latest, bool stats = false, Resolution resolution = Resolution.All, int quantity = 30, Orientation orientation = Orientation.All, CancellationToken cancellationToken = default)
</code></pre>
<p><strong>Examples:</strong></p>
<pre><code class="lang-csharp">// Get user's photos
var userPhotos = await client.GetUserPhotos(&quot;chrisjoelcampbell&quot;, page: 1, perPage: 20);

Console.WriteLine($&quot;Photos by user: {userPhotos.Count}&quot;);
foreach (var photo in userPhotos)
{
    Console.WriteLine($&quot;  {photo.Id}: {photo.Description ?? &quot;Untitled&quot;}&quot;);
    Console.WriteLine($&quot;    Dimensions: {photo.Width}x{photo.Height}&quot;);
    Console.WriteLine($&quot;    Likes: {photo.Likes}&quot;);
}

// Get user's popular photos
var popularPhotos = await client.GetUserPhotos(
    username: &quot;chrisjoelcampbell&quot;,
    orderBy: OrderBy.Popular,
    perPage: 10
);

// Get user's landscape photos only
var landscapePhotos = await client.GetUserPhotos(
    username: &quot;chrisjoelcampbell&quot;,
    orientation: Orientation.Landscape,
    perPage: 15
);
</code></pre>
<h3 id="getuserlikes--getuserlikesasync">GetUserLikes / GetUserLikesAsync</h3>
<p>Get photos liked by a specific user.</p>
<p><strong>Signatures:</strong></p>
<pre><code class="lang-csharp">Task&lt;List&lt;Photo&gt;&gt; GetUserLikes(string username, int page = 1, int perPage = 10, OrderBy orderBy = OrderBy.Latest, Orientation orientation = Orientation.All, CancellationToken cancellationToken = default)
</code></pre>
<p><strong>Examples:</strong></p>
<pre><code class="lang-csharp">// Get user's liked photos
var likedPhotos = await client.GetUserLikes(&quot;chrisjoelcampbell&quot;, page: 1, perPage: 20);

Console.WriteLine($&quot;Photos liked by user: {likedPhotos.Count}&quot;);
foreach (var photo in likedPhotos)
{
    Console.WriteLine($&quot;  {photo.Id} by {photo.User.Name}&quot;);
    Console.WriteLine($&quot;    {photo.Description ?? &quot;No description&quot;}&quot;);
}
</code></pre>
<h3 id="getusercollections--getusercollectionsasync">GetUserCollections / GetUserCollectionsAsync</h3>
<p>Get collections created by a specific user.</p>
<p><strong>Signatures:</strong></p>
<pre><code class="lang-csharp">Task&lt;List&lt;Collection&gt;&gt; GetUserCollections(string username, int page = 1, int perPage = 10, CancellationToken cancellationToken = default)
</code></pre>
<p><strong>Examples:</strong></p>
<pre><code class="lang-csharp">// Get user's collections
var userCollections = await client.GetUserCollections(&quot;chrisjoelcampbell&quot;);

Console.WriteLine($&quot;Collections by user: {userCollections.Count}&quot;);
foreach (var collection in userCollections)
{
    Console.WriteLine($&quot;  {collection.Title} ({collection.TotalPhotos} photos)&quot;);
    Console.WriteLine($&quot;    {collection.Description}&quot;);
}
</code></pre>
<h2 id="statistics-methods">Statistics Methods</h2>
<h3 id="gettotalstats--gettotalstatsasync">GetTotalStats / GetTotalStatsAsync</h3>
<p>Get overall Unsplash statistics.</p>
<p><strong>Signatures:</strong></p>
<pre><code class="lang-csharp">Task&lt;UnplashTotalStats?&gt; GetTotalStats(CancellationToken cancellationToken = default)
Task&lt;UnplashTotalStats&gt; GetTotalStatsAsync(CancellationToken cancellationToken = default)
</code></pre>
<p><strong>Examples:</strong></p>
<pre><code class="lang-csharp">// Get total platform statistics
var totalStats = await client.GetTotalStats();
if (totalStats != null)
{
    Console.WriteLine($&quot;Total photos: {totalStats.Photos:N0}&quot;);
    Console.WriteLine($&quot;Total downloads: {totalStats.Downloads:N0}&quot;);
    Console.WriteLine($&quot;Total views: {totalStats.Views:N0}&quot;);
    Console.WriteLine($&quot;Total likes: {totalStats.Likes:N0}&quot;);
    Console.WriteLine($&quot;Total photographers: {totalStats.Photographers:N0}&quot;);
    Console.WriteLine($&quot;Total pixels served: {totalStats.PixelsServed:N0}&quot;);
    Console.WriteLine($&quot;Total views this month: {totalStats.ViewsThisMonth:N0}&quot;);
    Console.WriteLine($&quot;Total new photos this month: {totalStats.NewPhotosThisMonth:N0}&quot;);
}
</code></pre>
<h3 id="getmonthlystats--getmonthlystatsasync">GetMonthlyStats / GetMonthlyStatsAsync</h3>
<p>Get Unsplash statistics for the past 30 days.</p>
<p><strong>Signatures:</strong></p>
<pre><code class="lang-csharp">Task&lt;UnplashMonthlyStats?&gt; GetMonthlyStats(CancellationToken cancellationToken = default)
Task&lt;UnplashMonthlyStats&gt; GetMonthlyStatsAsync(CancellationToken cancellationToken = default)
</code></pre>
<p><strong>Examples:</strong></p>
<pre><code class="lang-csharp">// Get monthly statistics
var monthlyStats = await client.GetMonthlyStats();
if (monthlyStats != null)
{
    Console.WriteLine($&quot;Downloads this month: {monthlyStats.Downloads:N0}&quot;);
    Console.WriteLine($&quot;Views this month: {monthlyStats.Views:N0}&quot;);
    Console.WriteLine($&quot;Likes this month: {monthlyStats.Likes:N0}&quot;);
    Console.WriteLine($&quot;New photos this month: {monthlyStats.NewPhotos:N0}&quot;);
    Console.WriteLine($&quot;New photographers this month: {monthlyStats.NewPhotographers:N0}&quot;);
    Console.WriteLine($&quot;New pixels this month: {monthlyStats.NewPixels:N0}&quot;);
    Console.WriteLine($&quot;New developers this month: {monthlyStats.NewDevelopers:N0}&quot;);
    Console.WriteLine($&quot;New applications this month: {monthlyStats.NewApplications:N0}&quot;);
    Console.WriteLine($&quot;New requests this month: {monthlyStats.NewRequests:N0}&quot;);
}
</code></pre>
<h2 id="method-parameters">Method Parameters</h2>
<h3 id="common-parameters">Common Parameters</h3>
<h4 id="pagination-parameters">Pagination Parameters</h4>
<ul>
<li><strong><code>page</code></strong> (int): Page number to retrieve (1-based indexing). Default: 1</li>
<li><strong><code>perPage</code></strong> (int): Number of items per page. Default: 10, Maximum: 30</li>
</ul>
<h4 id="ordering-parameters">Ordering Parameters</h4>
<ul>
<li><strong><code>orderBy</code></strong> (OrderBy enum):
<ul>
<li><code>OrderBy.Latest</code> - Most recent first (default)</li>
<li><code>OrderBy.Oldest</code> - Oldest first</li>
<li><code>OrderBy.Popular</code> - Most popular first</li>
<li><code>OrderBy.Relevant</code> - Most relevant (for search queries)</li>
</ul>
</li>
</ul>
<h4 id="orientation-parameters">Orientation Parameters</h4>
<ul>
<li><strong><code>orientation</code></strong> (Orientation enum):
<ul>
<li><code>Orientation.All</code> - All orientations (default)</li>
<li><code>Orientation.Landscape</code> - Landscape photos only</li>
<li><code>Orientation.Portrait</code> - Portrait photos only</li>
<li><code>Orientation.Squarish</code> - Square-ish photos only</li>
</ul>
</li>
</ul>
<h4 id="resolution-parameters">Resolution Parameters</h4>
<ul>
<li><strong><code>resolution</code></strong> (Resolution enum):
<ul>
<li><code>Resolution.All</code> - All resolutions (default)</li>
<li><code>Resolution.Regular</code> - Regular resolution</li>
<li><code>Resolution.Small</code> - Small resolution</li>
</ul>
</li>
</ul>
<h3 id="photo-specific-parameters">Photo-Specific Parameters</h3>
<h4 id="custom-sizing-parameters">Custom Sizing Parameters</h4>
<ul>
<li><strong><code>width</code></strong> (int?): Custom width in pixels</li>
<li><strong><code>height</code></strong> (int?): Custom height in pixels</li>
<li><strong><code>cropX</code></strong> (int?): X coordinate for cropping</li>
<li><strong><code>cropY</code></strong> (int?): Y coordinate for cropping</li>
<li><strong><code>cropWidth</code></strong> (int?): Width of crop area</li>
<li><strong><code>cropHeight</code></strong> (int?): Height of crop area</li>
</ul>
<h4 id="search-filters">Search Filters</h4>
<ul>
<li><strong><code>query</code></strong> (string): Search query string</li>
<li><strong><code>color</code></strong> (string?): Filter by color (&quot;black_and_white&quot;, &quot;black&quot;, &quot;white&quot;, &quot;yellow&quot;, &quot;orange&quot;, &quot;red&quot;, &quot;purple&quot;, &quot;magenta&quot;, &quot;green&quot;, &quot;teal&quot;, &quot;blue&quot;)</li>
<li><strong><code>contentFilter</code></strong> (string?): Content safety filter (&quot;low&quot;, &quot;high&quot;)</li>
<li><strong><code>collectionIds</code></strong> (string?): Comma-separated collection IDs to search within</li>
</ul>
<h4 id="random-photo-parameters">Random Photo Parameters</h4>
<ul>
<li><strong><code>count</code></strong> (int): Number of random photos to return (1-30)</li>
<li><strong><code>featured</code></strong> (bool): Only featured photos</li>
<li><strong><code>username</code></strong> (string?): Photos from specific user only</li>
<li><strong><code>collectionId</code></strong> (string?): Photos from specific collection only</li>
<li><strong><code>collectionIds</code></strong> (string[]): Photos from multiple collections</li>
</ul>
<h2 id="return-types">Return Types</h2>
<h3 id="photo-model">Photo Model</h3>
<p>The <code>Photo</code> class contains comprehensive information about an Unsplash photo:</p>
<pre><code class="lang-csharp">public class Photo
{
    public string Id { get; set; }                    // Unique photo identifier
    public string Description { get; set; }           // Photo description
    public string CreatedAt { get; set; }            // Creation timestamp
    public string UpdatedAt { get; set; }            // Last update timestamp
    public int Width { get; set; }                   // Photo width in pixels
    public int Height { get; set; }                 // Photo height in pixels
    public string Color { get; set; }               // Dominant color
    public string BlurHash { get; set; }            // BlurHash placeholder
    public int Downloads { get; set; }              // Download count
    public int Likes { get; set; }                  // Like count
    public bool IsLikedByUser { get; set; }         // User like status

    // Complex properties
    public Urls Urls { get; set; }                  // Photo URLs
    public User User { get; set; }                  // Photo author
    public Exif Exif { get; set; }                 // Camera EXIF data
    public Location Location { get; set; }          // Photo location
    public PhotoLinks Links { get; set; }           // Related links
    public List&lt;Category&gt; Categories { get; set; }  // Photo categories
    public List&lt;Collection&gt; CurrentUserCollection { get; set; } // User collections
}
</code></pre>
<h3 id="collection-model">Collection Model</h3>
<p>The <code>Collection</code> class represents a curated collection of photos:</p>
<pre><code class="lang-csharp">public class Collection
{
    public string Id { get; set; }                  // Unique collection identifier
    public string Title { get; set; }              // Collection title
    public string Description { get; set; }        // Collection description
    public string PublishedAt { get; set; }        // Publication timestamp
    public string UpdatedAt { get; set; }          // Last update timestamp
    public int TotalPhotos { get; set; }           // Number of photos
    public bool IsPrivate { get; set; }            // Privacy status
    public string ShareKey { get; set; }           // Share key for private collections

    // Complex properties
    public Photo CoverPhoto { get; set; }          // Collection cover photo
    public User User { get; set; }                // Collection creator
    public CollectionLinks Links { get; set; }     // Related links
}
</code></pre>
<h3 id="user-model">User Model</h3>
<p>The <code>User</code> class contains user profile information:</p>
<pre><code class="lang-csharp">public class User
{
    public string Id { get; set; }                 // Unique user identifier
    public string Username { get; set; }           // Username
    public string Name { get; set; }               // Display name
    public string FirstName { get; set; }          // First name
    public string LastName { get; set; }           // Last name
    public string TwitterUsername { get; set; }    // Twitter handle
    public string PortfolioUrl { get; set; }       // Portfolio URL
    public string Bio { get; set; }                // User biography
    public string Location { get; set; }           // User location
    public int TotalLikes { get; set; }            // Total likes received
    public int TotalPhotos { get; set; }           // Total photos uploaded
    public int TotalCollections { get; set; }      // Total collections created
    public bool ForHire { get; set; }              // Available for hire

    // Complex properties
    public ProfileImage ProfileImage { get; set; } // Profile image URLs
    public Badge Badge { get; set; }               // User badge info
    public UserLinks Links { get; set; }           // Related links
}
</code></pre>
<h3 id="url-types">URL Types</h3>
<p>Different photo sizes are available through the <code>Urls</code> class:</p>
<pre><code class="lang-csharp">public class Urls
{
    public string Raw { get; set; }        // Full resolution, uncompressed
    public string Full { get; set; }       // Large size (max 2048px)
    public string Regular { get; set; }    // Medium size (max 1080px)
    public string Small { get; set; }      // Small size (max 400px)
    public string Thumbnail { get; set; }  // Thumbnail (max 200px)
    public string Custom { get; set; }     // Custom size (when specified)
}
</code></pre>
<h2 id="rate-limiting">Rate Limiting</h2>
<h3 id="rate-limit-information">Rate Limit Information</h3>
<p>The client automatically tracks rate limit information:</p>
<pre><code class="lang-csharp">var client = new UnsplasharpClient(&quot;YOUR_APP_ID&quot;);

// Make a request
var photo = await client.GetRandomPhoto();

// Check rate limit status
Console.WriteLine($&quot;Rate limit: {client.RateLimitRemaining}/{client.MaxRateLimit}&quot;);
</code></pre>
<h3 id="rate-limit-headers">Rate Limit Headers</h3>
<p>Unsplash provides rate limit information in response headers:</p>
<ul>
<li><code>X-Ratelimit-Limit</code>: Maximum requests per hour</li>
<li><code>X-Ratelimit-Remaining</code>: Remaining requests in current hour</li>
</ul>
<h3 id="handling-rate-limits">Handling Rate Limits</h3>
<p>Use the exception-throwing methods for better rate limit handling:</p>
<pre><code class="lang-csharp">try
{
    var photos = await client.SearchPhotosAsync(&quot;nature&quot;);
}
catch (UnsplasharpRateLimitException ex)
{
    Console.WriteLine($&quot;Rate limit exceeded: {ex.RateLimitRemaining}/{ex.RateLimit}&quot;);
    Console.WriteLine($&quot;Reset time: {ex.RateLimitReset}&quot;);

    // Wait until reset or implement exponential backoff
    if (ex.RateLimitReset.HasValue)
    {
        var waitTime = ex.RateLimitReset.Value - DateTimeOffset.UtcNow;
        await Task.Delay(waitTime);
    }
}
</code></pre>
<h2 id="best-practices">Best Practices</h2>
<h3 id="1-use-exception-throwing-methods">1. Use Exception-Throwing Methods</h3>
<p>For new code, prefer the <code>*Async</code> methods that throw exceptions:</p>
<pre><code class="lang-csharp">// Preferred approach
try
{
    var photo = await client.GetPhotoAsync(&quot;photo-id&quot;);
    // Handle success
}
catch (UnsplasharpNotFoundException)
{
    // Handle not found
}
catch (UnsplasharpException ex)
{
    // Handle other errors
}
</code></pre>
<h3 id="2-implement-proper-error-handling">2. Implement Proper Error Handling</h3>
<p>Always handle potential exceptions appropriately:</p>
<pre><code class="lang-csharp">public async Task&lt;Photo?&gt; GetPhotoSafely(string photoId)
{
    try
    {
        return await client.GetPhotoAsync(photoId);
    }
    catch (UnsplasharpNotFoundException)
    {
        logger.LogWarning(&quot;Photo {PhotoId} not found&quot;, photoId);
        return null;
    }
    catch (UnsplasharpRateLimitException ex)
    {
        logger.LogWarning(&quot;Rate limit exceeded, retry after {ResetTime}&quot;, ex.RateLimitReset);
        throw; // Re-throw to handle at higher level
    }
    catch (UnsplasharpException ex)
    {
        logger.LogError(ex, &quot;Unsplash API error for photo {PhotoId}&quot;, photoId);
        throw;
    }
}
</code></pre>
<h3 id="3-use-cancellation-tokens">3. Use Cancellation Tokens</h3>
<p>Always pass cancellation tokens for better responsiveness:</p>
<pre><code class="lang-csharp">public async Task&lt;List&lt;Photo&gt;&gt; SearchWithTimeout(string query, TimeSpan timeout)
{
    using var cts = new CancellationTokenSource(timeout);

    try
    {
        return await client.SearchPhotosAsync(query, cancellationToken: cts.Token);
    }
    catch (OperationCanceledException)
    {
        logger.LogWarning(&quot;Search for '{Query}' timed out after {Timeout}&quot;, query, timeout);
        return new List&lt;Photo&gt;();
    }
}
</code></pre>
<h3 id="4-respect-rate-limits">4. Respect Rate Limits</h3>
<p>Monitor and respect API rate limits:</p>
<pre><code class="lang-csharp">public async Task&lt;List&lt;Photo&gt;&gt; GetPhotosWithRateLimit(List&lt;string&gt; photoIds)
{
    var photos = new List&lt;Photo&gt;();

    foreach (var photoId in photoIds)
    {
        // Check rate limit before making request
        if (client.RateLimitRemaining &lt; 10)
        {
            logger.LogWarning(&quot;Rate limit running low, pausing requests&quot;);
            await Task.Delay(TimeSpan.FromMinutes(1));
        }

        try
        {
            var photo = await client.GetPhotoAsync(photoId);
            photos.Add(photo);
        }
        catch (UnsplasharpRateLimitException)
        {
            logger.LogWarning(&quot;Rate limit exceeded, stopping batch operation&quot;);
            break;
        }
    }

    return photos;
}
</code></pre>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/rootasjey/unsplasharp/blob/master/docs/api-reference.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
