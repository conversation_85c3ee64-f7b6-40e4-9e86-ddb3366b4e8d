<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Code Examples and Recipes | Unsplasharp Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Code Examples and Recipes | Unsplasharp Documentation ">
      
      
      <link rel="icon" href="favicon.ico">
      <link rel="stylesheet" href="public/docfx.min.css">
      <link rel="stylesheet" href="public/main.css">
      <meta name="docfx:navrel" content="toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="">
      
      
      <meta name="docfx:docurl" content="https://github.com/rootasjey/unsplasharp/blob/master/docs/code-examples.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="index.html">
            <img id="logo" class="svg" src="logo.svg" alt="Unsplasharp">
            Unsplasharp
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">

      <div class="content">
        <div class="actionbar">

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="code-examples-and-recipes">Code Examples and Recipes</h1>

<p>This comprehensive collection provides practical code examples and recipes for common Unsplasharp use cases, from basic operations to advanced integration patterns.</p>
<h2 id="table-of-contents">Table of Contents</h2>
<ul>
<li><a href="#basic-operations">Basic Operations</a></li>
<li><a href="#search-and-discovery">Search and Discovery</a></li>
<li><a href="#user-and-collection-management">User and Collection Management</a></li>
<li><a href="#image-processing-and-download">Image Processing and Download</a></li>
<li><a href="#web-application-integration">Web Application Integration</a></li>
<li><a href="#desktop-application-examples">Desktop Application Examples</a></li>
<li><a href="#background-services">Background Services</a></li>
<li><a href="#testing-patterns">Testing Patterns</a></li>
<li><a href="#performance-optimization">Performance Optimization</a></li>
</ul>
<h2 id="basic-operations">Basic Operations</h2>
<h3 id="simple-photo-retrieval">Simple Photo Retrieval</h3>
<pre><code class="lang-csharp">// Get a random photo
public async Task&lt;string&gt; GetRandomPhotoUrl()
{
    var client = new UnsplasharpClient(&quot;YOUR_APP_ID&quot;);
    
    try
    {
        var photo = await client.GetRandomPhotoAsync();
        return photo.Urls.Regular;
    }
    catch (UnsplasharpException ex)
    {
        Console.WriteLine($&quot;Error: {ex.Message}&quot;);
        return &quot;https://via.placeholder.com/800x600?text=Photo+Not+Available&quot;;
    }
}

// Get a specific photo with error handling
public async Task&lt;Photo?&gt; GetPhotoSafely(string photoId)
{
    var client = new UnsplasharpClient(&quot;YOUR_APP_ID&quot;);
    
    try
    {
        return await client.GetPhotoAsync(photoId);
    }
    catch (UnsplasharpNotFoundException)
    {
        Console.WriteLine($&quot;Photo {photoId} not found&quot;);
        return null;
    }
    catch (UnsplasharpRateLimitException ex)
    {
        Console.WriteLine($&quot;Rate limited. Try again at {ex.RateLimitReset}&quot;);
        return null;
    }
    catch (UnsplasharpException ex)
    {
        Console.WriteLine($&quot;API error: {ex.Message}&quot;);
        return null;
    }
}
</code></pre>
<h3 id="photo-information-display">Photo Information Display</h3>
<pre><code class="lang-csharp">public static void DisplayPhotoInfo(Photo photo)
{
    Console.WriteLine(&quot;=== PHOTO INFORMATION ===&quot;);
    Console.WriteLine($&quot;ID: {photo.Id}&quot;);
    Console.WriteLine($&quot;Description: {photo.Description ?? &quot;No description&quot;}&quot;);
    Console.WriteLine($&quot;Photographer: {photo.User.Name} (@{photo.User.Username})&quot;);
    Console.WriteLine($&quot;Dimensions: {photo.Width}x{photo.Height}&quot;);
    Console.WriteLine($&quot;Likes: {photo.Likes:N0} | Downloads: {photo.Downloads:N0}&quot;);
    Console.WriteLine($&quot;Color: {photo.Color}&quot;);
    Console.WriteLine($&quot;Created: {DateTime.Parse(photo.CreatedAt):yyyy-MM-dd}&quot;);
    
    // URLs
    Console.WriteLine(&quot;\n=== AVAILABLE SIZES ===&quot;);
    Console.WriteLine($&quot;Thumbnail: {photo.Urls.Thumbnail}&quot;);
    Console.WriteLine($&quot;Small: {photo.Urls.Small}&quot;);
    Console.WriteLine($&quot;Regular: {photo.Urls.Regular}&quot;);
    Console.WriteLine($&quot;Full: {photo.Urls.Full}&quot;);
    Console.WriteLine($&quot;Raw: {photo.Urls.Raw}&quot;);
    
    // Location (if available)
    if (!string.IsNullOrEmpty(photo.Location.Name))
    {
        Console.WriteLine($&quot;\n=== LOCATION ===&quot;);
        Console.WriteLine($&quot;Location: {photo.Location.Name}&quot;);
        if (photo.Location.Position != null)
        {
            Console.WriteLine($&quot;Coordinates: {photo.Location.Position.Latitude}, {photo.Location.Position.Longitude}&quot;);
        }
    }
    
    // Camera info (if available)
    if (!string.IsNullOrEmpty(photo.Exif.Make))
    {
        Console.WriteLine($&quot;\n=== CAMERA INFO ===&quot;);
        Console.WriteLine($&quot;Camera: {photo.Exif.Make} {photo.Exif.Model}&quot;);
        Console.WriteLine($&quot;Settings: f/{photo.Exif.Aperture}, {photo.Exif.ExposureTime}s, ISO {photo.Exif.Iso}&quot;);
        Console.WriteLine($&quot;Focal Length: {photo.Exif.FocalLength}&quot;);
    }
}
</code></pre>
<h2 id="search-and-discovery">Search and Discovery</h2>
<h3 id="smart-search-with-fallbacks">Smart Search with Fallbacks</h3>
<pre><code class="lang-csharp">public class SmartPhotoSearch
{
    private readonly UnsplasharpClient _client;
    private readonly ILogger&lt;SmartPhotoSearch&gt; _logger;

    public SmartPhotoSearch(UnsplasharpClient client, ILogger&lt;SmartPhotoSearch&gt; logger)
    {
        _client = client;
        _logger = logger;
    }

    public async Task&lt;List&lt;Photo&gt;&gt; SearchWithFallbacks(string query, int desiredCount = 20)
    {
        var strategies = new List&lt;(string name, Func&lt;Task&lt;List&lt;Photo&gt;&gt;&gt; search)&gt;
        {
            (&quot;Exact match&quot;, () =&gt; _client.SearchPhotosAsync(query, perPage: desiredCount)),
            (&quot;Popular results&quot;, () =&gt; _client.SearchPhotosAsync(query, orderBy: OrderBy.Popular, perPage: desiredCount)),
            (&quot;Broader search&quot;, () =&gt; SearchBroaderTerms(query, desiredCount)),
            (&quot;Random fallback&quot;, () =&gt; GetRandomPhotosForQuery(query, desiredCount))
        };

        foreach (var (name, search) in strategies)
        {
            try
            {
                var results = await search();
                if (results.Count &gt; 0)
                {
                    _logger.LogInformation(&quot;Search strategy '{Strategy}' succeeded with {Count} results&quot;, name, results.Count);
                    return results;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, &quot;Search strategy '{Strategy}' failed&quot;, name);
            }
        }

        _logger.LogWarning(&quot;All search strategies failed for query: {Query}&quot;, query);
        return new List&lt;Photo&gt;();
    }

    private async Task&lt;List&lt;Photo&gt;&gt; SearchBroaderTerms(string query, int count)
    {
        var words = query.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        var allPhotos = new List&lt;Photo&gt;();

        foreach (var word in words.Take(3))
        {
            try
            {
                var photos = await _client.SearchPhotosAsync(word, perPage: count / words.Length + 5);
                allPhotos.AddRange(photos);
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, &quot;Failed to search for word: {Word}&quot;, word);
            }
        }

        return allPhotos.DistinctBy(p =&gt; p.Id).Take(count).ToList();
    }

    private async Task&lt;List&lt;Photo&gt;&gt; GetRandomPhotosForQuery(string query, int count)
    {
        try
        {
            return await _client.GetRandomPhotosAsync(count, query: query);
        }
        catch
        {
            // Final fallback - completely random photos
            return await _client.GetRandomPhotosAsync(count);
        }
    }
}
</code></pre>
<h3 id="advanced-search-filters">Advanced Search Filters</h3>
<pre><code class="lang-csharp">public class AdvancedPhotoSearch
{
    private readonly UnsplasharpClient _client;

    public AdvancedPhotoSearch(UnsplasharpClient client)
    {
        _client = client;
    }

    // Search for high-quality landscape photos
    public async Task&lt;List&lt;Photo&gt;&gt; GetHighQualityLandscapes(string query, int count = 20)
    {
        var photos = await _client.SearchPhotosAsync(
            query: $&quot;{query} landscape&quot;,
            orderBy: OrderBy.Popular,
            orientation: Orientation.Landscape,
            perPage: count * 2 // Get more to filter
        );

        return photos
            .Where(p =&gt; p.Width &gt;= 1920 &amp;&amp; p.Height &gt;= 1080) // HD or better
            .Where(p =&gt; p.Likes &gt;= 100) // Popular photos
            .Take(count)
            .ToList();
    }

    // Search for photos by color theme
    public async Task&lt;List&lt;Photo&gt;&gt; GetPhotosByColor(string query, string color, int count = 20)
    {
        return await _client.SearchPhotosAsync(
            query: query,
            color: color,
            orderBy: OrderBy.Popular,
            perPage: count
        );
    }

    // Search for portrait photos suitable for profiles
    public async Task&lt;List&lt;Photo&gt;&gt; GetPortraitPhotos(string query, int count = 20)
    {
        var photos = await _client.SearchPhotosAsync(
            query: $&quot;{query} portrait person face&quot;,
            orientation: Orientation.Portrait,
            contentFilter: &quot;high&quot;, // Safe content
            orderBy: OrderBy.Popular,
            perPage: count * 2
        );

        return photos
            .Where(p =&gt; p.Height &gt; p.Width) // Ensure portrait orientation
            .Where(p =&gt; p.Likes &gt;= 50) // Some popularity
            .Take(count)
            .ToList();
    }

    // Search within specific collections
    public async Task&lt;List&lt;Photo&gt;&gt; SearchInCollections(string query, string[] collectionIds, int count = 20)
    {
        var collectionIdsString = string.Join(&quot;,&quot;, collectionIds);
        
        return await _client.SearchPhotosAsync(
            query: query,
            collectionIds: collectionIdsString,
            orderBy: OrderBy.Relevant,
            perPage: count
        );
    }
}
</code></pre>
<h3 id="pagination-helper">Pagination Helper</h3>
<pre><code class="lang-csharp">public class PaginatedSearch
{
    private readonly UnsplasharpClient _client;

    public PaginatedSearch(UnsplasharpClient client)
    {
        _client = client;
    }

    public async IAsyncEnumerable&lt;Photo&gt; SearchAllPhotos(
        string query,
        int batchSize = 30,
        int maxPhotos = 1000,
        [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        int currentPage = 1;
        int totalReturned = 0;
        bool hasMoreResults = true;

        while (hasMoreResults &amp;&amp; totalReturned &lt; maxPhotos &amp;&amp; !cancellationToken.IsCancellationRequested)
        {
            try
            {
                var photos = await _client.SearchPhotosAsync(
                    query,
                    page: currentPage,
                    perPage: Math.Min(batchSize, maxPhotos - totalReturned),
                    cancellationToken: cancellationToken
                );

                if (photos.Count == 0)
                {
                    hasMoreResults = false;
                    yield break;
                }

                foreach (var photo in photos)
                {
                    if (totalReturned &gt;= maxPhotos)
                        yield break;

                    yield return photo;
                    totalReturned++;
                }

                hasMoreResults = photos.Count == batchSize &amp;&amp; 
                               currentPage &lt; _client.LastPhotosSearchTotalPages;
                currentPage++;

                // Rate limiting courtesy delay
                await Task.Delay(100, cancellationToken);
            }
            catch (UnsplasharpRateLimitException ex)
            {
                var delay = ex.TimeUntilReset ?? TimeSpan.FromMinutes(1);
                await Task.Delay(delay, cancellationToken);
            }
            catch (Exception)
            {
                hasMoreResults = false;
            }
        }
    }
}

// Usage example
public async Task SearchExample()
{
    var search = new PaginatedSearch(_client);
    var photoCount = 0;

    await foreach (var photo in search.SearchAllPhotos(&quot;nature&quot;, maxPhotos: 100))
    {
        Console.WriteLine($&quot;{++photoCount}: {photo.Description} by {photo.User.Name}&quot;);
        
        if (photoCount &gt;= 50) // Process first 50
            break;
    }
}
</code></pre>
<h2 id="user-and-collection-management">User and Collection Management</h2>
<h3 id="user-profile-analysis">User Profile Analysis</h3>
<pre><code class="lang-csharp">public class UserAnalyzer
{
    private readonly UnsplasharpClient _client;

    public UserAnalyzer(UnsplasharpClient client)
    {
        _client = client;
    }

    public async Task&lt;UserProfile&gt; AnalyzeUser(string username)
    {
        var user = await _client.GetUserAsync(username);
        var userPhotos = await _client.GetUserPhotosAsync(username, perPage: 30);
        var userLikes = await _client.GetUserLikesAsync(username, perPage: 30);

        return new UserProfile
        {
            User = user,
            RecentPhotos = userPhotos,
            RecentLikes = userLikes,
            EngagementRate = CalculateEngagementRate(user),
            AveragePhotoQuality = CalculateAverageQuality(userPhotos),
            PopularityScore = CalculatePopularityScore(user),
            ActivityLevel = DetermineActivityLevel(user, userPhotos)
        };
    }

    private double CalculateEngagementRate(User user)
    {
        return user.TotalPhotos &gt; 0 ? (double)user.TotalLikes / user.TotalPhotos : 0;
    }

    private double CalculateAverageQuality(List&lt;Photo&gt; photos)
    {
        if (!photos.Any()) return 0;

        return photos.Average(p =&gt; 
            (p.Likes * 0.4) + 
            (p.Downloads * 0.3) + 
            (p.Width &gt;= 1920 ? 20 : 0) + 
            (p.Height &gt;= 1080 ? 20 : 0)
        );
    }

    private int CalculatePopularityScore(User user)
    {
        var score = 0;
        
        if (user.TotalLikes &gt; 100000) score += 50;
        else if (user.TotalLikes &gt; 10000) score += 30;
        else if (user.TotalLikes &gt; 1000) score += 10;

        if (user.TotalPhotos &gt; 1000) score += 30;
        else if (user.TotalPhotos &gt; 100) score += 20;
        else if (user.TotalPhotos &gt; 10) score += 10;

        if (user.FollowersCount &gt; 10000) score += 20;
        else if (user.FollowersCount &gt; 1000) score += 10;

        return score;
    }

    private ActivityLevel DetermineActivityLevel(User user, List&lt;Photo&gt; recentPhotos)
    {
        if (!recentPhotos.Any()) return ActivityLevel.Inactive;

        var recentPhotoCount = recentPhotos.Count(p =&gt; 
            DateTime.Parse(p.CreatedAt) &gt; DateTime.UtcNow.AddDays(-30));

        return recentPhotoCount switch
        {
            &gt;= 10 =&gt; ActivityLevel.VeryActive,
            &gt;= 5 =&gt; ActivityLevel.Active,
            &gt;= 1 =&gt; ActivityLevel.Moderate,
            _ =&gt; ActivityLevel.Low
        };
    }
}

public class UserProfile
{
    public User User { get; set; }
    public List&lt;Photo&gt; RecentPhotos { get; set; } = new();
    public List&lt;Photo&gt; RecentLikes { get; set; } = new();
    public double EngagementRate { get; set; }
    public double AveragePhotoQuality { get; set; }
    public int PopularityScore { get; set; }
    public ActivityLevel ActivityLevel { get; set; }
}

public enum ActivityLevel
{
    Inactive,
    Low,
    Moderate,
    Active,
    VeryActive
}
</code></pre>
<h3 id="collection-explorer">Collection Explorer</h3>
<pre><code class="lang-csharp">public class CollectionExplorer
{
    private readonly UnsplasharpClient _client;

    public CollectionExplorer(UnsplasharpClient client)
    {
        _client = client;
    }

    public async Task&lt;CollectionAnalysis&gt; AnalyzeCollection(string collectionId)
    {
        var collection = await _client.GetCollectionAsync(collectionId);
        var photos = await _client.GetCollectionPhotosAsync(collectionId, perPage: 30);

        var analysis = new CollectionAnalysis
        {
            Collection = collection,
            SamplePhotos = photos,
            AveragePhotoQuality = photos.Any() ? photos.Average(p =&gt; p.Likes + p.Downloads) : 0,
            DominantColors = GetDominantColors(photos),
            CommonOrientations = GetOrientationDistribution(photos),
            TopPhotographers = GetTopPhotographers(photos),
            QualityScore = CalculateCollectionQuality(collection, photos)
        };

        return analysis;
    }

    private Dictionary&lt;string, int&gt; GetDominantColors(List&lt;Photo&gt; photos)
    {
        return photos
            .GroupBy(p =&gt; p.Color)
            .ToDictionary(g =&gt; g.Key, g =&gt; g.Count())
            .OrderByDescending(kvp =&gt; kvp.Value)
            .Take(5)
            .ToDictionary(kvp =&gt; kvp.Key, kvp =&gt; kvp.Value);
    }

    private Dictionary&lt;string, int&gt; GetOrientationDistribution(List&lt;Photo&gt; photos)
    {
        return photos
            .GroupBy(p =&gt; p.Width &gt; p.Height ? &quot;Landscape&quot; : 
                         p.Height &gt; p.Width ? &quot;Portrait&quot; : &quot;Square&quot;)
            .ToDictionary(g =&gt; g.Key, g =&gt; g.Count());
    }

    private List&lt;(string Name, int PhotoCount)&gt; GetTopPhotographers(List&lt;Photo&gt; photos)
    {
        return photos
            .GroupBy(p =&gt; p.User.Name)
            .Select(g =&gt; (Name: g.Key, PhotoCount: g.Count()))
            .OrderByDescending(x =&gt; x.PhotoCount)
            .Take(5)
            .ToList();
    }

    private int CalculateCollectionQuality(Collection collection, List&lt;Photo&gt; photos)
    {
        var score = 0;
        
        // Collection size scoring
        if (collection.TotalPhotos &gt; 100) score += 20;
        else if (collection.TotalPhotos &gt; 50) score += 15;
        else if (collection.TotalPhotos &gt; 10) score += 10;

        // Photo quality scoring
        if (photos.Any())
        {
            var avgLikes = photos.Average(p =&gt; p.Likes);
            if (avgLikes &gt; 1000) score += 30;
            else if (avgLikes &gt; 100) score += 20;
            else if (avgLikes &gt; 10) score += 10;

            var highResCount = photos.Count(p =&gt; p.Width &gt;= 1920 &amp;&amp; p.Height &gt;= 1080);
            score += (highResCount * 100 / photos.Count) / 5; // Up to 20 points
        }

        // Curator reputation
        var curator = collection.User;
        if (curator.TotalLikes &gt; 10000) score += 15;
        else if (curator.TotalLikes &gt; 1000) score += 10;

        return Math.Min(score, 100); // Cap at 100
    }
}

public class CollectionAnalysis
{
    public Collection Collection { get; set; }
    public List&lt;Photo&gt; SamplePhotos { get; set; } = new();
    public double AveragePhotoQuality { get; set; }
    public Dictionary&lt;string, int&gt; DominantColors { get; set; } = new();
    public Dictionary&lt;string, int&gt; CommonOrientations { get; set; } = new();
    public List&lt;(string Name, int PhotoCount)&gt; TopPhotographers { get; set; } = new();
    public int QualityScore { get; set; }
}
</code></pre>
<h2 id="image-processing-and-download">Image Processing and Download</h2>
<h3 id="smart-image-downloader">Smart Image Downloader</h3>
<pre><code class="lang-csharp">public class SmartImageDownloader
{
    private readonly UnsplasharpClient _client;
    private readonly HttpClient _httpClient;
    private readonly ILogger&lt;SmartImageDownloader&gt; _logger;

    public SmartImageDownloader(UnsplasharpClient client, HttpClient httpClient, ILogger&lt;SmartImageDownloader&gt; logger)
    {
        _client = client;
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task&lt;DownloadResult&gt; DownloadOptimalSize(string photoId, int maxWidth, int maxHeight, string downloadPath)
    {
        try
        {
            var photo = await _client.GetPhotoAsync(photoId);
            var optimalUrl = SelectOptimalUrl(photo.Urls, maxWidth, maxHeight);

            _logger.LogInformation(&quot;Downloading photo {PhotoId} from {Url}&quot;, photoId, optimalUrl);

            var imageBytes = await _httpClient.GetByteArrayAsync(optimalUrl);
            var fileName = GenerateFileName(photo, optimalUrl);
            var fullPath = Path.Combine(downloadPath, fileName);

            Directory.CreateDirectory(downloadPath);
            await File.WriteAllBytesAsync(fullPath, imageBytes);

            return new DownloadResult
            {
                Success = true,
                FilePath = fullPath,
                FileSize = imageBytes.Length,
                Photo = photo,
                DownloadUrl = optimalUrl
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, &quot;Failed to download photo {PhotoId}&quot;, photoId);
            return new DownloadResult { Success = false, Error = ex.Message };
        }
    }

    private string SelectOptimalUrl(Urls urls, int maxWidth, int maxHeight)
    {
        var maxDimension = Math.Max(maxWidth, maxHeight);

        return maxDimension switch
        {
            &lt;= 200 =&gt; urls.Thumbnail,
            &lt;= 400 =&gt; urls.Small,
            &lt;= 1080 =&gt; urls.Regular,
            &lt;= 2048 =&gt; urls.Full,
            _ =&gt; urls.Raw
        };
    }

    private string GenerateFileName(Photo photo, string url)
    {
        var extension = url.Contains(&quot;fm=&quot;) &amp;&amp; url.Contains(&quot;fm=webp&quot;) ? &quot;webp&quot; : &quot;jpg&quot;;
        var sanitizedDescription = SanitizeFileName(photo.Description ?? &quot;untitled&quot;);
        var timestamp = DateTime.Now.ToString(&quot;yyyyMMdd_HHmmss&quot;);

        return $&quot;{photo.Id}_{sanitizedDescription}_{timestamp}.{extension}&quot;;
    }

    private string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = new string(fileName.Where(c =&gt; !invalidChars.Contains(c)).ToArray());
        return sanitized.Length &gt; 50 ? sanitized.Substring(0, 50) : sanitized;
    }

    public async Task&lt;BatchDownloadResult&gt; DownloadMultiplePhotos(
        IEnumerable&lt;string&gt; photoIds,
        string downloadPath,
        int maxWidth = 1920,
        int maxHeight = 1080,
        int maxConcurrency = 3)
    {
        var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
        var result = new BatchDownloadResult();

        var downloadTasks = photoIds.Select(async photoId =&gt;
        {
            await semaphore.WaitAsync();
            try
            {
                var downloadResult = await DownloadOptimalSize(photoId, maxWidth, maxHeight, downloadPath);

                lock (result)
                {
                    if (downloadResult.Success)
                        result.SuccessfulDownloads.Add(downloadResult);
                    else
                        result.FailedDownloads.Add(photoId, downloadResult.Error ?? &quot;Unknown error&quot;);
                }
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(downloadTasks);
        return result;
    }
}

public class DownloadResult
{
    public bool Success { get; set; }
    public string? FilePath { get; set; }
    public long FileSize { get; set; }
    public Photo? Photo { get; set; }
    public string? DownloadUrl { get; set; }
    public string? Error { get; set; }
}

public class BatchDownloadResult
{
    public List&lt;DownloadResult&gt; SuccessfulDownloads { get; } = new();
    public Dictionary&lt;string, string&gt; FailedDownloads { get; } = new();

    public int TotalAttempted =&gt; SuccessfulDownloads.Count + FailedDownloads.Count;
    public double SuccessRate =&gt; TotalAttempted &gt; 0 ? (double)SuccessfulDownloads.Count / TotalAttempted : 0;
}
</code></pre>
<h3 id="image-metadata-extractor">Image Metadata Extractor</h3>
<pre><code class="lang-csharp">public class ImageMetadataExtractor
{
    public static ImageMetadata ExtractMetadata(Photo photo)
    {
        return new ImageMetadata
        {
            Id = photo.Id,
            Title = photo.Description ?? &quot;Untitled&quot;,
            Photographer = photo.User.Name,
            PhotographerUsername = photo.User.Username,
            Dimensions = new Size(photo.Width, photo.Height),
            AspectRatio = (double)photo.Width / photo.Height,
            DominantColor = photo.Color,
            BlurHash = photo.BlurHash,
            CreatedAt = DateTime.Parse(photo.CreatedAt),
            Engagement = new EngagementMetrics
            {
                Likes = photo.Likes,
                Downloads = photo.Downloads,
                IsLikedByUser = photo.IsLikedByUser
            },
            Camera = ExtractCameraInfo(photo.Exif),
            Location = ExtractLocationInfo(photo.Location),
            Keywords = ExtractKeywords(photo),
            QualityScore = CalculateQualityScore(photo)
        };
    }

    private static CameraInfo? ExtractCameraInfo(Exif exif)
    {
        if (string.IsNullOrEmpty(exif.Make)) return null;

        return new CameraInfo
        {
            Make = exif.Make,
            Model = exif.Model,
            Aperture = exif.Aperture,
            ExposureTime = exif.ExposureTime,
            Iso = exif.Iso,
            FocalLength = exif.FocalLength
        };
    }

    private static LocationInfo? ExtractLocationInfo(Location location)
    {
        if (string.IsNullOrEmpty(location.Name)) return null;

        return new LocationInfo
        {
            Name = location.Name,
            City = location.City,
            Country = location.Country,
            Coordinates = location.Position != null
                ? new Coordinates(location.Position.Latitude, location.Position.Longitude)
                : null
        };
    }

    private static List&lt;string&gt; ExtractKeywords(Photo photo)
    {
        var keywords = new List&lt;string&gt;();

        // Add categories
        keywords.AddRange(photo.Categories.Select(c =&gt; c.Title));

        // Extract from description
        if (!string.IsNullOrEmpty(photo.Description))
        {
            var words = photo.Description.Split(' ', StringSplitOptions.RemoveEmptyEntries)
                .Where(w =&gt; w.Length &gt; 3)
                .Select(w =&gt; w.Trim('.', ',', '!', '?').ToLowerInvariant())
                .Distinct();
            keywords.AddRange(words);
        }

        // Add orientation
        keywords.Add(photo.Width &gt; photo.Height ? &quot;landscape&quot; :
                    photo.Height &gt; photo.Width ? &quot;portrait&quot; : &quot;square&quot;);

        // Add color
        keywords.Add($&quot;color-{photo.Color.TrimStart('#')}&quot;);

        return keywords.Distinct().ToList();
    }

    private static int CalculateQualityScore(Photo photo)
    {
        var score = 0;

        // Resolution scoring
        var pixels = photo.Width * photo.Height;
        if (pixels &gt;= 8000000) score += 25; // 8MP+
        else if (pixels &gt;= 2000000) score += 20; // 2MP+
        else if (pixels &gt;= 1000000) score += 15; // 1MP+
        else score += 10;

        // Engagement scoring
        if (photo.Likes &gt; 10000) score += 25;
        else if (photo.Likes &gt; 1000) score += 20;
        else if (photo.Likes &gt; 100) score += 15;
        else score += 10;

        // Technical quality
        if (!string.IsNullOrEmpty(photo.Exif.Make)) score += 15; // Has EXIF
        if (!string.IsNullOrEmpty(photo.Location.Name)) score += 10; // Has location
        if (!string.IsNullOrEmpty(photo.Description)) score += 10; // Has description

        // Photographer reputation
        if (photo.User.TotalLikes &gt; 100000) score += 15;
        else if (photo.User.TotalLikes &gt; 10000) score += 10;
        else if (photo.User.TotalLikes &gt; 1000) score += 5;

        return Math.Min(score, 100);
    }
}

public class ImageMetadata
{
    public string Id { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Photographer { get; set; } = string.Empty;
    public string PhotographerUsername { get; set; } = string.Empty;
    public Size Dimensions { get; set; }
    public double AspectRatio { get; set; }
    public string DominantColor { get; set; } = string.Empty;
    public string BlurHash { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public EngagementMetrics Engagement { get; set; } = new();
    public CameraInfo? Camera { get; set; }
    public LocationInfo? Location { get; set; }
    public List&lt;string&gt; Keywords { get; set; } = new();
    public int QualityScore { get; set; }
}

public record Size(int Width, int Height);
public record Coordinates(double Latitude, double Longitude);

public class EngagementMetrics
{
    public int Likes { get; set; }
    public int Downloads { get; set; }
    public bool IsLikedByUser { get; set; }
}

public class CameraInfo
{
    public string Make { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public string Aperture { get; set; } = string.Empty;
    public string ExposureTime { get; set; } = string.Empty;
    public int Iso { get; set; }
    public string FocalLength { get; set; } = string.Empty;
}

public class LocationInfo
{
    public string Name { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public Coordinates? Coordinates { get; set; }
}
</code></pre>
<h2 id="web-application-integration">Web Application Integration</h2>
<h3 id="aspnet-core-photo-api">ASP.NET Core Photo API</h3>
<pre><code class="lang-csharp">[ApiController]
[Route(&quot;api/[controller]&quot;)]
public class PhotosController : ControllerBase
{
    private readonly UnsplasharpClient _unsplashClient;
    private readonly IMemoryCache _cache;
    private readonly ILogger&lt;PhotosController&gt; _logger;

    public PhotosController(UnsplasharpClient unsplashClient, IMemoryCache cache, ILogger&lt;PhotosController&gt; logger)
    {
        _unsplashClient = unsplashClient;
        _cache = cache;
        _logger = logger;
    }

    [HttpGet(&quot;random&quot;)]
    public async Task&lt;ActionResult&lt;PhotoDto&gt;&gt; GetRandomPhoto([FromQuery] string? query = null)
    {
        try
        {
            var photo = string.IsNullOrEmpty(query)
                ? await _unsplashClient.GetRandomPhotoAsync()
                : await _unsplashClient.GetRandomPhotoAsync(query: query);

            return Ok(PhotoDto.FromPhoto(photo));
        }
        catch (UnsplasharpRateLimitException ex)
        {
            _logger.LogWarning(&quot;Rate limit exceeded: {RemainingRequests}/{TotalRequests}&quot;,
                ex.RateLimitRemaining, ex.RateLimit);
            return StatusCode(429, new { error = &quot;Rate limit exceeded&quot;, retryAfter = ex.TimeUntilReset });
        }
        catch (UnsplasharpException ex)
        {
            _logger.LogError(ex, &quot;Error getting random photo&quot;);
            return StatusCode(500, new { error = &quot;Failed to fetch photo&quot; });
        }
    }

    [HttpGet(&quot;search&quot;)]
    public async Task&lt;ActionResult&lt;SearchResultDto&gt;&gt; SearchPhotos(
        [FromQuery] string query,
        [FromQuery] int page = 1,
        [FromQuery] int perPage = 20,
        [FromQuery] string? color = null,
        [FromQuery] string? orientation = null)
    {
        if (string.IsNullOrWhiteSpace(query))
            return BadRequest(new { error = &quot;Query parameter is required&quot; });

        var cacheKey = $&quot;search:{query}:{page}:{perPage}:{color}:{orientation}&quot;;

        if (_cache.TryGetValue(cacheKey, out SearchResultDto cachedResult))
        {
            return Ok(cachedResult);
        }

        try
        {
            var orientationEnum = orientation?.ToLowerInvariant() switch
            {
                &quot;landscape&quot; =&gt; Orientation.Landscape,
                &quot;portrait&quot; =&gt; Orientation.Portrait,
                &quot;squarish&quot; =&gt; Orientation.Squarish,
                _ =&gt; Orientation.All
            };

            var photos = await _unsplashClient.SearchPhotosAsync(
                query: query,
                page: page,
                perPage: Math.Min(perPage, 30), // Limit max per page
                color: color,
                orientation: orientationEnum
            );

            var result = new SearchResultDto
            {
                Photos = photos.Select(PhotoDto.FromPhoto).ToList(),
                TotalResults = _unsplashClient.LastPhotosSearchTotalResults,
                TotalPages = _unsplashClient.LastPhotosSearchTotalPages,
                CurrentPage = page,
                Query = query
            };

            // Cache for 5 minutes
            _cache.Set(cacheKey, result, TimeSpan.FromMinutes(5));

            return Ok(result);
        }
        catch (UnsplasharpException ex)
        {
            _logger.LogError(ex, &quot;Error searching photos with query: {Query}&quot;, query);
            return StatusCode(500, new { error = &quot;Search failed&quot; });
        }
    }

    [HttpGet(&quot;{photoId}&quot;)]
    public async Task&lt;ActionResult&lt;PhotoDto&gt;&gt; GetPhoto(string photoId)
    {
        var cacheKey = $&quot;photo:{photoId}&quot;;

        if (_cache.TryGetValue(cacheKey, out PhotoDto cachedPhoto))
        {
            return Ok(cachedPhoto);
        }

        try
        {
            var photo = await _unsplashClient.GetPhotoAsync(photoId);
            var photoDto = PhotoDto.FromPhoto(photo);

            // Cache for 1 hour
            _cache.Set(cacheKey, photoDto, TimeSpan.FromHours(1));

            return Ok(photoDto);
        }
        catch (UnsplasharpNotFoundException)
        {
            return NotFound(new { error = $&quot;Photo with ID '{photoId}' not found&quot; });
        }
        catch (UnsplasharpException ex)
        {
            _logger.LogError(ex, &quot;Error getting photo: {PhotoId}&quot;, photoId);
            return StatusCode(500, new { error = &quot;Failed to fetch photo&quot; });
        }
    }
}

public class PhotoDto
{
    public string Id { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string PhotographerName { get; set; } = string.Empty;
    public string PhotographerUsername { get; set; } = string.Empty;
    public int Width { get; set; }
    public int Height { get; set; }
    public string Color { get; set; } = string.Empty;
    public int Likes { get; set; }
    public int Downloads { get; set; }
    public DateTime CreatedAt { get; set; }
    public UrlsDto Urls { get; set; } = new();

    public static PhotoDto FromPhoto(Photo photo)
    {
        return new PhotoDto
        {
            Id = photo.Id,
            Description = photo.Description ?? string.Empty,
            PhotographerName = photo.User.Name,
            PhotographerUsername = photo.User.Username,
            Width = photo.Width,
            Height = photo.Height,
            Color = photo.Color,
            Likes = photo.Likes,
            Downloads = photo.Downloads,
            CreatedAt = DateTime.Parse(photo.CreatedAt),
            Urls = new UrlsDto
            {
                Thumbnail = photo.Urls.Thumbnail,
                Small = photo.Urls.Small,
                Regular = photo.Urls.Regular,
                Full = photo.Urls.Full
            }
        };
    }
}

public class UrlsDto
{
    public string Thumbnail { get; set; } = string.Empty;
    public string Small { get; set; } = string.Empty;
    public string Regular { get; set; } = string.Empty;
    public string Full { get; set; } = string.Empty;
}

public class SearchResultDto
{
    public List&lt;PhotoDto&gt; Photos { get; set; } = new();
    public int TotalResults { get; set; }
    public int TotalPages { get; set; }
    public int CurrentPage { get; set; }
    public string Query { get; set; } = string.Empty;
}
</code></pre>
<h2 id="desktop-application-examples">Desktop Application Examples</h2>
<h3 id="wpf-photo-gallery">WPF Photo Gallery</h3>
<pre><code class="lang-csharp">public partial class PhotoGalleryWindow : Window
{
    private readonly UnsplasharpClient _client;
    private readonly ObservableCollection&lt;PhotoViewModel&gt; _photos;
    private CancellationTokenSource? _searchCancellation;

    public PhotoGalleryWindow()
    {
        InitializeComponent();
        _client = new UnsplasharpClient(&quot;YOUR_APP_ID&quot;);
        _photos = new ObservableCollection&lt;PhotoViewModel&gt;();
        PhotosListView.ItemsSource = _photos;
    }

    private async void SearchButton_Click(object sender, RoutedEventArgs e)
    {
        var query = SearchTextBox.Text.Trim();
        if (string.IsNullOrEmpty(query)) return;

        // Cancel previous search
        _searchCancellation?.Cancel();
        _searchCancellation = new CancellationTokenSource();

        LoadingProgressBar.Visibility = Visibility.Visible;
        SearchButton.IsEnabled = false;
        _photos.Clear();

        try
        {
            var photos = await _client.SearchPhotosAsync(query, perPage: 30, cancellationToken: _searchCancellation.Token);

            foreach (var photo in photos)
            {
                _photos.Add(new PhotoViewModel(photo));
            }

            StatusTextBlock.Text = $&quot;Found {_client.LastPhotosSearchTotalResults:N0} photos&quot;;
        }
        catch (OperationCanceledException)
        {
            StatusTextBlock.Text = &quot;Search cancelled&quot;;
        }
        catch (UnsplasharpRateLimitException ex)
        {
            StatusTextBlock.Text = $&quot;Rate limited. Try again at {ex.RateLimitReset:HH:mm}&quot;;
        }
        catch (UnsplasharpException ex)
        {
            StatusTextBlock.Text = $&quot;Error: {ex.Message}&quot;;
        }
        finally
        {
            LoadingProgressBar.Visibility = Visibility.Collapsed;
            SearchButton.IsEnabled = true;
        }
    }

    private async void DownloadButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button &amp;&amp; button.DataContext is PhotoViewModel photoVM)
        {
            var saveDialog = new Microsoft.Win32.SaveFileDialog
            {
                FileName = $&quot;{photoVM.Id}_{photoVM.PhotographerName}.jpg&quot;,
                Filter = &quot;JPEG Image|*.jpg|All Files|*.*&quot;
            };

            if (saveDialog.ShowDialog() == true)
            {
                button.IsEnabled = false;
                button.Content = &quot;Downloading...&quot;;

                try
                {
                    using var httpClient = new HttpClient();
                    var imageBytes = await httpClient.GetByteArrayAsync(photoVM.RegularUrl);
                    await File.WriteAllBytesAsync(saveDialog.FileName, imageBytes);

                    MessageBox.Show(&quot;Photo downloaded successfully!&quot;, &quot;Success&quot;, MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($&quot;Download failed: {ex.Message}&quot;, &quot;Error&quot;, MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    button.Content = &quot;Download&quot;;
                    button.IsEnabled = true;
                }
            }
        }
    }

    private void RandomPhotoButton_Click(object sender, RoutedEventArgs e)
    {
        _ = LoadRandomPhoto();
    }

    private async Task LoadRandomPhoto()
    {
        LoadingProgressBar.Visibility = Visibility.Visible;

        try
        {
            var photo = await _client.GetRandomPhotoAsync();
            _photos.Clear();
            _photos.Add(new PhotoViewModel(photo));
            StatusTextBlock.Text = &quot;Random photo loaded&quot;;
        }
        catch (UnsplasharpException ex)
        {
            StatusTextBlock.Text = $&quot;Error: {ex.Message}&quot;;
        }
        finally
        {
            LoadingProgressBar.Visibility = Visibility.Collapsed;
        }
    }
}

public class PhotoViewModel : INotifyPropertyChanged
{
    public string Id { get; }
    public string Description { get; }
    public string PhotographerName { get; }
    public string PhotographerUsername { get; }
    public int Width { get; }
    public int Height { get; }
    public string Color { get; }
    public int Likes { get; }
    public string ThumbnailUrl { get; }
    public string SmallUrl { get; }
    public string RegularUrl { get; }
    public string AspectRatioText { get; }
    public string DimensionsText { get; }
    public string EngagementText { get; }

    public PhotoViewModel(Photo photo)
    {
        Id = photo.Id;
        Description = photo.Description ?? &quot;Untitled&quot;;
        PhotographerName = photo.User.Name;
        PhotographerUsername = photo.User.Username;
        Width = photo.Width;
        Height = photo.Height;
        Color = photo.Color;
        Likes = photo.Likes;
        ThumbnailUrl = photo.Urls.Thumbnail;
        SmallUrl = photo.Urls.Small;
        RegularUrl = photo.Urls.Regular;

        AspectRatioText = $&quot;{(double)Width / Height:F2}:1&quot;;
        DimensionsText = $&quot;{Width}×{Height}&quot;;
        EngagementText = $&quot;{Likes:N0} likes&quot;;
    }

    public event PropertyChangedEventHandler? PropertyChanged;
}
</code></pre>
<h3 id="console-photo-browser">Console Photo Browser</h3>
<pre><code class="lang-csharp">public class ConsolePhotoBrowser
{
    private readonly UnsplasharpClient _client;
    private readonly List&lt;Photo&gt; _currentPhotos = new();
    private int _currentIndex = 0;

    public ConsolePhotoBrowser(string applicationId)
    {
        _client = new UnsplasharpClient(applicationId);
    }

    public async Task RunAsync()
    {
        Console.WriteLine(&quot;=== Unsplash Photo Browser ===&quot;);
        Console.WriteLine(&quot;Commands: search &lt;query&gt;, random, next, prev, info, download, quit&quot;);
        Console.WriteLine();

        while (true)
        {
            Console.Write(&quot;&gt; &quot;);
            var input = Console.ReadLine()?.Trim().ToLowerInvariant();

            if (string.IsNullOrEmpty(input)) continue;

            var parts = input.Split(' ', 2);
            var command = parts[0];
            var argument = parts.Length &gt; 1 ? parts[1] : string.Empty;

            try
            {
                switch (command)
                {
                    case &quot;search&quot;:
                        if (string.IsNullOrEmpty(argument))
                        {
                            Console.WriteLine(&quot;Usage: search &lt;query&gt;&quot;);
                            break;
                        }
                        await SearchPhotos(argument);
                        break;

                    case &quot;random&quot;:
                        await LoadRandomPhoto();
                        break;

                    case &quot;next&quot;:
                        ShowNextPhoto();
                        break;

                    case &quot;prev&quot;:
                        ShowPreviousPhoto();
                        break;

                    case &quot;info&quot;:
                        ShowCurrentPhotoInfo();
                        break;

                    case &quot;download&quot;:
                        await DownloadCurrentPhoto();
                        break;

                    case &quot;quit&quot;:
                    case &quot;exit&quot;:
                        return;

                    default:
                        Console.WriteLine(&quot;Unknown command. Available: search, random, next, prev, info, download, quit&quot;);
                        break;
                }
            }
            catch (UnsplasharpRateLimitException ex)
            {
                Console.WriteLine($&quot;Rate limited. Try again at {ex.RateLimitReset:HH:mm:ss}&quot;);
            }
            catch (UnsplasharpException ex)
            {
                Console.WriteLine($&quot;Error: {ex.Message}&quot;);
            }
            catch (Exception ex)
            {
                Console.WriteLine($&quot;Unexpected error: {ex.Message}&quot;);
            }

            Console.WriteLine();
        }
    }

    private async Task SearchPhotos(string query)
    {
        Console.WriteLine($&quot;Searching for '{query}'...&quot;);

        var photos = await _client.SearchPhotosAsync(query, perPage: 20);
        _currentPhotos.Clear();
        _currentPhotos.AddRange(photos);
        _currentIndex = 0;

        Console.WriteLine($&quot;Found {_client.LastPhotosSearchTotalResults:N0} photos ({photos.Count} loaded)&quot;);

        if (photos.Count &gt; 0)
        {
            ShowCurrentPhoto();
        }
    }

    private async Task LoadRandomPhoto()
    {
        Console.WriteLine(&quot;Loading random photo...&quot;);

        var photo = await _client.GetRandomPhotoAsync();
        _currentPhotos.Clear();
        _currentPhotos.Add(photo);
        _currentIndex = 0;

        ShowCurrentPhoto();
    }

    private void ShowNextPhoto()
    {
        if (_currentPhotos.Count == 0)
        {
            Console.WriteLine(&quot;No photos loaded. Use 'search' or 'random' first.&quot;);
            return;
        }

        _currentIndex = (_currentIndex + 1) % _currentPhotos.Count;
        ShowCurrentPhoto();
    }

    private void ShowPreviousPhoto()
    {
        if (_currentPhotos.Count == 0)
        {
            Console.WriteLine(&quot;No photos loaded. Use 'search' or 'random' first.&quot;);
            return;
        }

        _currentIndex = _currentIndex == 0 ? _currentPhotos.Count - 1 : _currentIndex - 1;
        ShowCurrentPhoto();
    }

    private void ShowCurrentPhoto()
    {
        if (_currentPhotos.Count == 0) return;

        var photo = _currentPhotos[_currentIndex];

        Console.WriteLine($&quot;Photo {_currentIndex + 1}/{_currentPhotos.Count}:&quot;);
        Console.WriteLine($&quot;  ID: {photo.Id}&quot;);
        Console.WriteLine($&quot;  Title: {photo.Description ?? &quot;Untitled&quot;}&quot;);
        Console.WriteLine($&quot;  By: {photo.User.Name} (@{photo.User.Username})&quot;);
        Console.WriteLine($&quot;  Size: {photo.Width}×{photo.Height}&quot;);
        Console.WriteLine($&quot;  Likes: {photo.Likes:N0}&quot;);
        Console.WriteLine($&quot;  URL: {photo.Urls.Regular}&quot;);
    }

    private void ShowCurrentPhotoInfo()
    {
        if (_currentPhotos.Count == 0)
        {
            Console.WriteLine(&quot;No photo selected.&quot;);
            return;
        }

        var photo = _currentPhotos[_currentIndex];
        DisplayPhotoInfo(photo); // Use the method from earlier examples
    }

    private async Task DownloadCurrentPhoto()
    {
        if (_currentPhotos.Count == 0)
        {
            Console.WriteLine(&quot;No photo selected.&quot;);
            return;
        }

        var photo = _currentPhotos[_currentIndex];
        var fileName = $&quot;{photo.Id}_{photo.User.Username}.jpg&quot;;

        Console.WriteLine($&quot;Downloading {fileName}...&quot;);

        try
        {
            using var httpClient = new HttpClient();
            var imageBytes = await httpClient.GetByteArrayAsync(photo.Urls.Regular);
            await File.WriteAllBytesAsync(fileName, imageBytes);

            Console.WriteLine($&quot;Downloaded to {fileName} ({imageBytes.Length:N0} bytes)&quot;);
        }
        catch (Exception ex)
        {
            Console.WriteLine($&quot;Download failed: {ex.Message}&quot;);
        }
    }
}

// Usage
class Program
{
    static async Task Main(string[] args)
    {
        var browser = new ConsolePhotoBrowser(&quot;YOUR_APP_ID&quot;);
        await browser.RunAsync();
    }
}
</code></pre>
<h2 id="background-services">Background Services</h2>
<h3 id="photo-sync-service">Photo Sync Service</h3>
<pre><code class="lang-csharp">public class PhotoSyncService : BackgroundService
{
    private readonly UnsplasharpClient _client;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger&lt;PhotoSyncService&gt; _logger;
    private readonly PhotoSyncOptions _options;

    public PhotoSyncService(
        UnsplasharpClient client,
        IServiceProvider serviceProvider,
        ILogger&lt;PhotoSyncService&gt; logger,
        IOptions&lt;PhotoSyncOptions&gt; options)
    {
        _client = client;
        _serviceProvider = serviceProvider;
        _logger = logger;
        _options = options.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation(&quot;Photo sync service started&quot;);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await SyncPhotos(stoppingToken);
                await Task.Delay(_options.SyncInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, &quot;Error during photo sync&quot;);
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // Wait before retry
            }
        }

        _logger.LogInformation(&quot;Photo sync service stopped&quot;);
    }

    private async Task SyncPhotos(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var photoRepository = scope.ServiceProvider.GetRequiredService&lt;IPhotoRepository&gt;();

        foreach (var query in _options.SyncQueries)
        {
            try
            {
                _logger.LogInformation(&quot;Syncing photos for query: {Query}&quot;, query);

                var photos = await _client.SearchPhotosAsync(query, perPage: _options.PhotosPerQuery, cancellationToken: cancellationToken);

                foreach (var photo in photos)
                {
                    await SyncSinglePhoto(photo, photoRepository, cancellationToken);
                }

                _logger.LogInformation(&quot;Synced {Count} photos for query: {Query}&quot;, photos.Count, query);
            }
            catch (UnsplasharpRateLimitException ex)
            {
                _logger.LogWarning(&quot;Rate limited during sync, waiting {Delay}ms&quot;, ex.TimeUntilReset?.TotalMilliseconds);

                if (ex.TimeUntilReset.HasValue)
                {
                    await Task.Delay(ex.TimeUntilReset.Value, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, &quot;Error syncing photos for query: {Query}&quot;, query);
            }
        }
    }

    private async Task SyncSinglePhoto(Photo photo, IPhotoRepository repository, CancellationToken cancellationToken)
    {
        try
        {
            var existingPhoto = await repository.GetByIdAsync(photo.Id);

            if (existingPhoto == null)
            {
                // New photo
                var photoEntity = MapToEntity(photo);
                await repository.AddAsync(photoEntity);
                _logger.LogDebug(&quot;Added new photo: {PhotoId}&quot;, photo.Id);
            }
            else if (existingPhoto.UpdatedAt &lt; DateTime.Parse(photo.UpdatedAt))
            {
                // Updated photo
                var updatedEntity = MapToEntity(photo);
                await repository.UpdateAsync(updatedEntity);
                _logger.LogDebug(&quot;Updated photo: {PhotoId}&quot;, photo.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, &quot;Error syncing photo: {PhotoId}&quot;, photo.Id);
        }
    }

    private PhotoEntity MapToEntity(Photo photo)
    {
        return new PhotoEntity
        {
            Id = photo.Id,
            Description = photo.Description,
            Width = photo.Width,
            Height = photo.Height,
            Color = photo.Color,
            Likes = photo.Likes,
            Downloads = photo.Downloads,
            CreatedAt = DateTime.Parse(photo.CreatedAt),
            UpdatedAt = DateTime.Parse(photo.UpdatedAt),
            PhotographerName = photo.User.Name,
            PhotographerUsername = photo.User.Username,
            ThumbnailUrl = photo.Urls.Thumbnail,
            SmallUrl = photo.Urls.Small,
            RegularUrl = photo.Urls.Regular,
            FullUrl = photo.Urls.Full,
            LastSyncedAt = DateTime.UtcNow
        };
    }
}

public class PhotoSyncOptions
{
    public TimeSpan SyncInterval { get; set; } = TimeSpan.FromHours(1);
    public List&lt;string&gt; SyncQueries { get; set; } = new() { &quot;nature&quot;, &quot;technology&quot;, &quot;business&quot; };
    public int PhotosPerQuery { get; set; } = 30;
}

public interface IPhotoRepository
{
    Task&lt;PhotoEntity?&gt; GetByIdAsync(string id);
    Task AddAsync(PhotoEntity photo);
    Task UpdateAsync(PhotoEntity photo);
}

public class PhotoEntity
{
    public string Id { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public string Color { get; set; } = string.Empty;
    public int Likes { get; set; }
    public int Downloads { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string PhotographerName { get; set; } = string.Empty;
    public string PhotographerUsername { get; set; } = string.Empty;
    public string ThumbnailUrl { get; set; } = string.Empty;
    public string SmallUrl { get; set; } = string.Empty;
    public string RegularUrl { get; set; } = string.Empty;
    public string FullUrl { get; set; } = string.Empty;
    public DateTime LastSyncedAt { get; set; }
}

// Registration in Program.cs
services.Configure&lt;PhotoSyncOptions&gt;(configuration.GetSection(&quot;PhotoSync&quot;));
services.AddHostedService&lt;PhotoSyncService&gt;();
</code></pre>
<h2 id="testing-patterns">Testing Patterns</h2>
<h3 id="unit-testing-with-mocking">Unit Testing with Mocking</h3>
<pre><code class="lang-csharp">[TestFixture]
public class PhotoServiceTests
{
    private Mock&lt;UnsplasharpClient&gt; _mockClient;
    private Mock&lt;ILogger&lt;PhotoService&gt;&gt; _mockLogger;
    private PhotoService _photoService;

    [SetUp]
    public void Setup()
    {
        _mockClient = new Mock&lt;UnsplasharpClient&gt;(&quot;test-app-id&quot;);
        _mockLogger = new Mock&lt;ILogger&lt;PhotoService&gt;&gt;();
        _photoService = new PhotoService(_mockClient.Object, _mockLogger.Object);
    }

    [Test]
    public async Task GetRandomPhoto_Success_ReturnsPhoto()
    {
        // Arrange
        var expectedPhoto = CreateTestPhoto();
        _mockClient.Setup(c =&gt; c.GetRandomPhotoAsync(It.IsAny&lt;CancellationToken&gt;()))
                  .ReturnsAsync(expectedPhoto);

        // Act
        var result = await _photoService.GetRandomPhotoAsync();

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(expectedPhoto.Id, result.Id);
        Assert.AreEqual(expectedPhoto.Description, result.Description);
    }

    [Test]
    public async Task GetRandomPhoto_RateLimited_ReturnsNull()
    {
        // Arrange
        _mockClient.Setup(c =&gt; c.GetRandomPhotoAsync(It.IsAny&lt;CancellationToken&gt;()))
                  .ThrowsAsync(new UnsplasharpRateLimitException(&quot;Rate limited&quot;, null, null, null, null, null));

        // Act
        var result = await _photoService.GetRandomPhotoAsync();

        // Assert
        Assert.IsNull(result);

        // Verify logging
        _mockLogger.Verify(
            x =&gt; x.Log(
                LogLevel.Warning,
                It.IsAny&lt;EventId&gt;(),
                It.Is&lt;It.IsAnyType&gt;((v, t) =&gt; v.ToString().Contains(&quot;Rate limited&quot;)),
                It.IsAny&lt;Exception&gt;(),
                It.IsAny&lt;Func&lt;It.IsAnyType, Exception, string&gt;&gt;()),
            Times.Once);
    }

    [Test]
    public async Task SearchPhotos_WithValidQuery_ReturnsPhotos()
    {
        // Arrange
        var query = &quot;nature&quot;;
        var expectedPhotos = new List&lt;Photo&gt; { CreateTestPhoto(), CreateTestPhoto() };

        _mockClient.Setup(c =&gt; c.SearchPhotosAsync(query, It.IsAny&lt;int&gt;(), It.IsAny&lt;int&gt;(),
                                                  It.IsAny&lt;OrderBy&gt;(), It.IsAny&lt;string&gt;(),
                                                  It.IsAny&lt;string&gt;(), It.IsAny&lt;string&gt;(),
                                                  It.IsAny&lt;Orientation&gt;(), It.IsAny&lt;CancellationToken&gt;()))
                  .ReturnsAsync(expectedPhotos);

        // Act
        var result = await _photoService.SearchPhotosAsync(query);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(expectedPhotos.Count, result.Count);
    }

    private Photo CreateTestPhoto()
    {
        return new Photo
        {
            Id = Guid.NewGuid().ToString(),
            Description = &quot;Test photo&quot;,
            Width = 1920,
            Height = 1080,
            Color = &quot;#FF5733&quot;,
            Likes = 100,
            Downloads = 500,
            CreatedAt = DateTime.UtcNow.ToString(&quot;O&quot;),
            UpdatedAt = DateTime.UtcNow.ToString(&quot;O&quot;),
            User = new User
            {
                Id = Guid.NewGuid().ToString(),
                Name = &quot;Test Photographer&quot;,
                Username = &quot;testuser&quot;
            },
            Urls = new Urls
            {
                Thumbnail = &quot;https://example.com/thumb.jpg&quot;,
                Small = &quot;https://example.com/small.jpg&quot;,
                Regular = &quot;https://example.com/regular.jpg&quot;,
                Full = &quot;https://example.com/full.jpg&quot;,
                Raw = &quot;https://example.com/raw.jpg&quot;
            }
        };
    }
}

public class PhotoService
{
    private readonly UnsplasharpClient _client;
    private readonly ILogger&lt;PhotoService&gt; _logger;

    public PhotoService(UnsplasharpClient client, ILogger&lt;PhotoService&gt; logger)
    {
        _client = client;
        _logger = logger;
    }

    public async Task&lt;Photo?&gt; GetRandomPhotoAsync()
    {
        try
        {
            return await _client.GetRandomPhotoAsync();
        }
        catch (UnsplasharpRateLimitException ex)
        {
            _logger.LogWarning(&quot;Rate limited: {Message}&quot;, ex.Message);
            return null;
        }
        catch (UnsplasharpException ex)
        {
            _logger.LogError(ex, &quot;Error getting random photo&quot;);
            throw;
        }
    }

    public async Task&lt;List&lt;Photo&gt;&gt; SearchPhotosAsync(string query)
    {
        try
        {
            return await _client.SearchPhotosAsync(query);
        }
        catch (UnsplasharpException ex)
        {
            _logger.LogError(ex, &quot;Error searching photos with query: {Query}&quot;, query);
            throw;
        }
    }
}
</code></pre>
<h3 id="integration-testing">Integration Testing</h3>
<pre><code class="lang-csharp">[TestFixture]
public class UnsplashIntegrationTests
{
    private UnsplasharpClient _client;
    private readonly string _testApplicationId = Environment.GetEnvironmentVariable(&quot;UNSPLASH_APP_ID&quot;) ?? &quot;test-app-id&quot;;

    [SetUp]
    public void Setup()
    {
        _client = new UnsplasharpClient(_testApplicationId);
    }

    [Test]
    [Category(&quot;Integration&quot;)]
    public async Task GetRandomPhoto_ReturnsValidPhoto()
    {
        // Act
        var photo = await _client.GetRandomPhotoAsync();

        // Assert
        Assert.IsNotNull(photo);
        Assert.IsNotEmpty(photo.Id);
        Assert.IsNotNull(photo.User);
        Assert.IsNotEmpty(photo.User.Name);
        Assert.IsNotNull(photo.Urls);
        Assert.IsNotEmpty(photo.Urls.Regular);
        Assert.Greater(photo.Width, 0);
        Assert.Greater(photo.Height, 0);
    }

    [Test]
    [Category(&quot;Integration&quot;)]
    public async Task SearchPhotos_WithValidQuery_ReturnsResults()
    {
        // Arrange
        var query = &quot;nature&quot;;

        // Act
        var photos = await _client.SearchPhotosAsync(query, perPage: 5);

        // Assert
        Assert.IsNotNull(photos);
        Assert.IsNotEmpty(photos);
        Assert.LessOrEqual(photos.Count, 5);

        foreach (var photo in photos)
        {
            Assert.IsNotEmpty(photo.Id);
            Assert.IsNotNull(photo.User);
            Assert.IsNotNull(photo.Urls);
        }
    }

    [Test]
    [Category(&quot;Integration&quot;)]
    public async Task GetPhoto_WithInvalidId_ThrowsNotFoundException()
    {
        // Arrange
        var invalidId = &quot;invalid-photo-id-12345&quot;;

        // Act &amp; Assert
        var ex = await Assert.ThrowsAsync&lt;UnsplasharpNotFoundException&gt;(
            () =&gt; _client.GetPhotoAsync(invalidId));

        Assert.IsNotNull(ex.Context);
        Assert.AreEqual(invalidId, ex.ResourceId);
    }

    [Test]
    [Category(&quot;Integration&quot;)]
    [Retry(3)] // Retry in case of rate limiting
    public async Task RateLimitHandling_MultipleRequests_HandlesGracefully()
    {
        var tasks = new List&lt;Task&lt;Photo&gt;&gt;();

        // Create multiple concurrent requests
        for (int i = 0; i &lt; 10; i++)
        {
            tasks.Add(_client.GetRandomPhotoAsync());
        }

        // Some requests might fail due to rate limiting, but shouldn't crash
        var results = await Task.WhenAll(tasks.Select(async task =&gt;
        {
            try
            {
                return await task;
            }
            catch (UnsplasharpRateLimitException)
            {
                return null; // Expected for some requests
            }
        }));

        // At least some requests should succeed
        var successfulResults = results.Where(r =&gt; r != null).ToList();
        Assert.Greater(successfulResults.Count, 0);
    }
}
</code></pre>
<h2 id="performance-optimization">Performance Optimization</h2>
<h3 id="caching-strategy">Caching Strategy</h3>
<pre><code class="lang-csharp">public class OptimizedPhotoService
{
    private readonly UnsplasharpClient _client;
    private readonly IMemoryCache _memoryCache;
    private readonly IDistributedCache _distributedCache;
    private readonly ILogger&lt;OptimizedPhotoService&gt; _logger;
    private readonly SemaphoreSlim _semaphore;

    public OptimizedPhotoService(
        UnsplasharpClient client,
        IMemoryCache memoryCache,
        IDistributedCache distributedCache,
        ILogger&lt;OptimizedPhotoService&gt; logger)
    {
        _client = client;
        _memoryCache = memoryCache;
        _distributedCache = distributedCache;
        _logger = logger;
        _semaphore = new SemaphoreSlim(5, 5); // Limit concurrent requests
    }

    public async Task&lt;Photo?&gt; GetPhotoOptimizedAsync(string photoId)
    {
        var cacheKey = $&quot;photo:{photoId}&quot;;

        // Try memory cache first (fastest)
        if (_memoryCache.TryGetValue(cacheKey, out Photo cachedPhoto))
        {
            _logger.LogDebug(&quot;Photo {PhotoId} found in memory cache&quot;, photoId);
            return cachedPhoto;
        }

        // Try distributed cache
        var distributedData = await _distributedCache.GetStringAsync(cacheKey);
        if (distributedData != null)
        {
            try
            {
                var photo = JsonSerializer.Deserialize&lt;Photo&gt;(distributedData);

                // Store in memory cache for faster future access
                _memoryCache.Set(cacheKey, photo, TimeSpan.FromMinutes(15));

                _logger.LogDebug(&quot;Photo {PhotoId} found in distributed cache&quot;, photoId);
                return photo;
            }
            catch (JsonException ex)
            {
                _logger.LogWarning(ex, &quot;Failed to deserialize cached photo {PhotoId}&quot;, photoId);
            }
        }

        // Fetch from API with concurrency control
        await _semaphore.WaitAsync();
        try
        {
            var photo = await _client.GetPhotoAsync(photoId);

            // Cache with intelligent TTL
            var memoryCacheDuration = CalculateCacheDuration(photo);
            _memoryCache.Set(cacheKey, photo, memoryCacheDuration);

            // Store in distributed cache
            var serializedPhoto = JsonSerializer.Serialize(photo);
            await _distributedCache.SetStringAsync(cacheKey, serializedPhoto,
                new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = memoryCacheDuration.Multiply(4)
                });

            _logger.LogDebug(&quot;Photo {PhotoId} fetched from API and cached&quot;, photoId);
            return photo;
        }
        catch (UnsplasharpNotFoundException)
        {
            // Cache negative results
            _memoryCache.Set(cacheKey, (Photo?)null, TimeSpan.FromMinutes(5));
            return null;
        }
        finally
        {
            _semaphore.Release();
        }
    }

    public async Task&lt;List&lt;Photo&gt;&gt; SearchPhotosOptimizedAsync(string query, int page = 1, int perPage = 20)
    {
        var cacheKey = $&quot;search:{query}:{page}:{perPage}&quot;;

        // Check cache first
        if (_memoryCache.TryGetValue(cacheKey, out List&lt;Photo&gt; cachedResults))
        {
            return cachedResults;
        }

        await _semaphore.WaitAsync();
        try
        {
            var photos = await _client.SearchPhotosAsync(query, page: page, perPage: perPage);

            // Cache search results for shorter time
            _memoryCache.Set(cacheKey, photos, TimeSpan.FromMinutes(5));

            // Pre-cache individual photos
            foreach (var photo in photos)
            {
                var photoCacheKey = $&quot;photo:{photo.Id}&quot;;
                _memoryCache.Set(photoCacheKey, photo, CalculateCacheDuration(photo));
            }

            return photos;
        }
        finally
        {
            _semaphore.Release();
        }
    }

    private TimeSpan CalculateCacheDuration(Photo photo)
    {
        // Popular photos cached longer
        var popularity = photo.Likes + (photo.Downloads / 10);

        return popularity switch
        {
            &gt; 10000 =&gt; TimeSpan.FromHours(2),
            &gt; 1000 =&gt; TimeSpan.FromHours(1),
            &gt; 100 =&gt; TimeSpan.FromMinutes(30),
            _ =&gt; TimeSpan.FromMinutes(15)
        };
    }
}
</code></pre>
<h3 id="batch-processing-optimization">Batch Processing Optimization</h3>
<pre><code class="lang-csharp">public class BatchPhotoProcessor
{
    private readonly UnsplasharpClient _client;
    private readonly ILogger&lt;BatchPhotoProcessor&gt; _logger;
    private readonly SemaphoreSlim _semaphore;

    public BatchPhotoProcessor(UnsplasharpClient client, ILogger&lt;BatchPhotoProcessor&gt; logger)
    {
        _client = client;
        _logger = logger;
        _semaphore = new SemaphoreSlim(3, 3); // Limit concurrent API calls
    }

    public async Task&lt;List&lt;Photo&gt;&gt; ProcessPhotosBatch(IEnumerable&lt;string&gt; photoIds, CancellationToken cancellationToken = default)
    {
        var results = new ConcurrentBag&lt;Photo&gt;();
        var batches = photoIds.Chunk(10); // Process in batches of 10

        foreach (var batch in batches)
        {
            var batchTasks = batch.Select(async photoId =&gt;
            {
                await _semaphore.WaitAsync(cancellationToken);
                try
                {
                    var photo = await _client.GetPhotoAsync(photoId, cancellationToken);
                    results.Add(photo);

                    _logger.LogDebug(&quot;Processed photo {PhotoId}&quot;, photoId);
                }
                catch (UnsplasharpNotFoundException)
                {
                    _logger.LogWarning(&quot;Photo {PhotoId} not found&quot;, photoId);
                }
                catch (UnsplasharpRateLimitException ex)
                {
                    _logger.LogWarning(&quot;Rate limited, waiting {Delay}ms&quot;, ex.TimeUntilReset?.TotalMilliseconds);

                    if (ex.TimeUntilReset.HasValue)
                    {
                        await Task.Delay(ex.TimeUntilReset.Value, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, &quot;Error processing photo {PhotoId}&quot;, photoId);
                }
                finally
                {
                    _semaphore.Release();
                }
            });

            await Task.WhenAll(batchTasks);

            // Rate limiting courtesy delay between batches
            await Task.Delay(500, cancellationToken);
        }

        return results.ToList();
    }
}
</code></pre>
<h3 id="connection-pool-optimization">Connection Pool Optimization</h3>
<pre><code class="lang-csharp">public static class UnsplashHttpOptimization
{
    public static void ConfigureOptimizedHttpClient(this IServiceCollection services, string applicationId)
    {
        services.AddHttpClient(&quot;unsplash&quot;, client =&gt;
        {
            client.BaseAddress = new Uri(&quot;https://api.unsplash.com/&quot;);
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(&quot;Client-ID&quot;, applicationId);
            client.Timeout = TimeSpan.FromSeconds(30);

            // Optimize headers
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(&quot;application/json&quot;));
            client.DefaultRequestHeaders.AcceptEncoding.Add(new StringWithQualityHeaderValue(&quot;gzip&quot;));
            client.DefaultRequestHeaders.AcceptEncoding.Add(new StringWithQualityHeaderValue(&quot;deflate&quot;));
        })
        .ConfigurePrimaryHttpMessageHandler(() =&gt; new SocketsHttpHandler
        {
            // Connection pooling optimization
            PooledConnectionLifetime = TimeSpan.FromMinutes(15),
            PooledConnectionIdleTimeout = TimeSpan.FromMinutes(5),
            MaxConnectionsPerServer = 10,

            // Performance settings
            EnableMultipleHttp2Connections = true,
            UseCookies = false,
            AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,

            // DNS optimization
            UseProxy = false
        });
    }
}
</code></pre>
<hr>
<p>This comprehensive collection of code examples and recipes covers the most common use cases for Unsplasharp, from basic operations to advanced integration patterns. Each example includes proper error handling, performance considerations, and follows modern C# best practices.</p>
<p>Key takeaways:</p>
<ul>
<li>Always implement proper error handling with specific exception types</li>
<li>Use caching strategies to improve performance and reduce API calls</li>
<li>Implement rate limiting awareness in your applications</li>
<li>Consider using dependency injection for better testability</li>
<li>Use cancellation tokens for responsive applications</li>
<li>Follow async/await best practices throughout your code</li>
</ul>
<pre><code></code></pre>
<pre><code></code></pre>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/rootasjey/unsplasharp/blob/master/docs/code-examples.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
