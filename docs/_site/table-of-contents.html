<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Unsplasharp Documentation - Table of Contents | Unsplasharp Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Unsplasharp Documentation - Table of Contents | Unsplasharp Documentation ">
      
      
      <link rel="icon" href="favicon.ico">
      <link rel="stylesheet" href="public/docfx.min.css">
      <link rel="stylesheet" href="public/main.css">
      <meta name="docfx:navrel" content="toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="">
      
      
      <meta name="docfx:docurl" content="https://github.com/rootasjey/unsplasharp/blob/master/docs/table-of-contents.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="index.html">
            <img id="logo" class="svg" src="logo.svg" alt="Unsplasharp">
            Unsplasharp
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">

      <div class="content">
        <div class="actionbar">

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="unsplasharp-documentation---table-of-contents">Unsplasharp Documentation - Table of Contents</h1>

<p>Complete overview of all Unsplasharp documentation with detailed section breakdowns.</p>
<h2 id="-complete-documentation-index">📋 Complete Documentation Index</h2>
<h3 id="-main-documentation-hub">🏠 <a href="index.html">Main Documentation Hub</a></h3>
<ul>
<li>Quick start guide</li>
<li>Core documentation overview</li>
<li>Navigation by use case and experience level</li>
<li>API overview and features</li>
<li>Installation instructions</li>
<li>External resources</li>
</ul>
<h3 id="-getting-started-guide">🚀 <a href="docs/getting-started.html">Getting Started Guide</a></h3>
<ul>
<li>Prerequisites and installation</li>
<li>Getting your API key</li>
<li>Basic setup (Console, ASP.NET Core)</li>
<li>Your first request</li>
<li>Common use cases with examples</li>
<li>Error handling introduction</li>
<li>Best practices overview</li>
<li>Next steps and sample projects</li>
</ul>
<h3 id="-api-reference-guide">🔧 <a href="api-reference.html">API Reference Guide</a></h3>
<ul>
<li><strong>Client Initialization</strong>
<ul>
<li>Basic initialization</li>
<li>Dependency injection setup</li>
</ul>
</li>
<li><strong>Photo Methods</strong>
<ul>
<li>GetPhoto / GetPhotoAsync</li>
<li>GetRandomPhoto / GetRandomPhotoAsync</li>
<li>ListPhotos / ListPhotosAsync</li>
</ul>
</li>
<li><strong>Search Methods</strong>
<ul>
<li>SearchPhotos / SearchPhotosAsync</li>
</ul>
</li>
<li><strong>Collection Methods</strong>
<ul>
<li>GetCollection / GetCollectionAsync</li>
<li>ListCollections / ListCollectionsAsync</li>
<li>GetCollectionPhotos / GetCollectionPhotosAsync</li>
<li>SearchCollections / SearchCollectionsAsync</li>
</ul>
</li>
<li><strong>User Methods</strong>
<ul>
<li>GetUser / GetUserAsync</li>
<li>GetUserPhotos / GetUserPhotosAsync</li>
<li>GetUserLikes / GetUserLikesAsync</li>
<li>GetUserCollections / GetUserCollectionsAsync</li>
</ul>
</li>
<li><strong>Statistics Methods</strong>
<ul>
<li>GetTotalStats / GetTotalStatsAsync</li>
<li>GetMonthlyStats / GetMonthlyStatsAsync</li>
</ul>
</li>
<li><strong>Method Parameters</strong>
<ul>
<li>Common parameters</li>
<li>Photo-specific parameters</li>
<li>Search filters</li>
</ul>
</li>
<li><strong>Return Types</strong>
<ul>
<li>Photo model</li>
<li>Collection model</li>
<li>User model</li>
<li>URL types</li>
</ul>
</li>
<li><strong>Rate Limiting</strong>
<ul>
<li>Rate limit information</li>
<li>Handling rate limits</li>
</ul>
</li>
<li><strong>Best Practices</strong>
<ul>
<li>Exception-throwing methods</li>
<li>Error handling patterns</li>
<li>Cancellation tokens</li>
<li>Rate limit monitoring</li>
</ul>
</li>
</ul>
<h3 id="-models-reference-guide">📊 <a href="models-reference.html">Models Reference Guide</a></h3>
<ul>
<li><strong>Photo Model</strong>
<ul>
<li>Properties and usage examples</li>
<li>Photo filtering and analysis</li>
</ul>
</li>
<li><strong>User Model</strong>
<ul>
<li>Properties and usage examples</li>
<li>User analysis utilities</li>
</ul>
</li>
<li><strong>Collection Model</strong>
<ul>
<li>Properties and usage examples</li>
</ul>
</li>
<li><strong>URL Models</strong>
<ul>
<li>Urls class</li>
<li>ProfileImage class</li>
<li>URL optimization helpers</li>
</ul>
</li>
<li><strong>Location and EXIF Models</strong>
<ul>
<li>Location class with GPS data</li>
<li>EXIF class with camera data</li>
<li>Analysis utilities</li>
</ul>
</li>
<li><strong>Statistics Models</strong>
<ul>
<li>UnplashTotalStats class</li>
<li>UnplashMonthlyStats class</li>
<li>Usage examples</li>
</ul>
</li>
<li><strong>Link Models</strong>
<ul>
<li>PhotoLinks, UserLinks, CollectionLinks</li>
</ul>
</li>
<li><strong>Supporting Models</strong>
<ul>
<li>Category, Badge classes</li>
</ul>
</li>
<li><strong>Model Relationships</strong>
<ul>
<li>Relationship diagram</li>
<li>Navigation examples</li>
</ul>
</li>
<li><strong>Usage Examples</strong>
<ul>
<li>Complete photo analysis</li>
</ul>
</li>
</ul>
<h3 id="-advanced-usage-patterns">🚀 <a href="advanced-usage.html">Advanced Usage Patterns</a></h3>
<ul>
<li><strong>Advanced Pagination Strategies</strong>
<ul>
<li>Infinite scroll implementation</li>
<li>Parallel pagination</li>
</ul>
</li>
<li><strong>Filtering and Search Optimization</strong>
<ul>
<li>Advanced search builder</li>
<li>Smart search with fallbacks</li>
</ul>
</li>
<li><strong>Custom Parameters and URL Manipulation</strong>
<ul>
<li>Custom photo sizing</li>
<li>URL parameter optimization</li>
</ul>
</li>
<li><strong>Performance Optimization</strong>
<ul>
<li>Intelligent caching strategy</li>
<li>Connection pooling and HTTP optimization</li>
</ul>
</li>
<li><strong>Batch Operations</strong>
<ul>
<li>Efficient bulk photo processing</li>
<li>Batch download manager</li>
</ul>
</li>
<li><strong>Monitoring and Metrics</strong>
<ul>
<li>Performance metrics collection</li>
</ul>
</li>
</ul>
<h3 id="-code-examples-and-recipes">💻 <a href="code-examples.html">Code Examples and Recipes</a></h3>
<ul>
<li><strong>Basic Operations</strong>
<ul>
<li>Simple photo retrieval</li>
<li>Photo information display</li>
</ul>
</li>
<li><strong>Search and Discovery</strong>
<ul>
<li>Smart search with fallbacks</li>
<li>Advanced search filters</li>
<li>Pagination helper</li>
</ul>
</li>
<li><strong>User and Collection Management</strong>
<ul>
<li>User profile analysis</li>
<li>Collection explorer</li>
</ul>
</li>
<li><strong>Image Processing and Download</strong>
<ul>
<li>Smart image downloader</li>
<li>Image metadata extractor</li>
</ul>
</li>
<li><strong>Web Application Integration</strong>
<ul>
<li>ASP.NET Core photo API</li>
</ul>
</li>
<li><strong>Desktop Application Examples</strong>
<ul>
<li>WPF photo gallery</li>
<li>Console photo browser</li>
</ul>
</li>
<li><strong>Background Services</strong>
<ul>
<li>Photo sync service</li>
</ul>
</li>
<li><strong>Testing Patterns</strong>
<ul>
<li>Unit testing with mocking</li>
<li>Integration testing</li>
</ul>
</li>
<li><strong>Performance Optimization</strong>
<ul>
<li>Caching strategies</li>
<li>Batch processing</li>
<li>Connection pool optimization</li>
</ul>
</li>
</ul>
<h3 id="-testing-and-best-practices-guide">🧪 <a href="testing-best-practices.html">Testing and Best Practices Guide</a></h3>
<ul>
<li><strong>Testing Strategies</strong>
<ul>
<li>Test pyramid approach</li>
<li>Testing infrastructure setup</li>
</ul>
</li>
<li><strong>Unit Testing</strong>
<ul>
<li>Success scenarios</li>
<li>Error scenarios</li>
<li>Caching behavior</li>
</ul>
</li>
<li><strong>Integration Testing</strong>
<ul>
<li>API contract testing</li>
<li>Rate limit testing</li>
</ul>
</li>
<li><strong>Performance Testing</strong>
<ul>
<li>Load testing</li>
<li>Memory usage testing</li>
</ul>
</li>
<li><strong>Best Practices</strong>
<ul>
<li>Error handling patterns</li>
<li>Caching strategies</li>
<li>Rate limiting approaches</li>
<li>Dependency injection</li>
</ul>
</li>
<li><strong>Common Pitfalls</strong>
<ul>
<li>Rate limit handling mistakes</li>
<li>HttpClient usage issues</li>
<li>Cancellation token omissions</li>
</ul>
</li>
<li><strong>Security Considerations</strong>
<ul>
<li>API key management</li>
<li>Input validation</li>
<li>User rate limiting</li>
</ul>
</li>
<li><strong>Production Deployment</strong>
<ul>
<li>Configuration management</li>
<li>Health checks</li>
<li>Graceful degradation</li>
</ul>
</li>
<li><strong>Monitoring and Observability</strong>
<ul>
<li>Structured logging</li>
<li>Custom metrics</li>
<li>Application Insights integration</li>
</ul>
</li>
</ul>
<h3 id="-migration-and-upgrade-guide">🔄 <a href="migration-guide.html">Migration and Upgrade Guide</a></h3>
<ul>
<li><strong>Version Compatibility</strong>
<ul>
<li>Supported .NET versions</li>
<li>API compatibility</li>
</ul>
</li>
<li><strong>Breaking Changes</strong> (None!)</li>
<li><strong>New Features Overview</strong>
<ul>
<li>Comprehensive error handling</li>
<li>IHttpClientFactory integration</li>
<li>Structured logging</li>
<li>Enhanced async support</li>
</ul>
</li>
<li><strong>Migration Strategies</strong>
<ul>
<li>Gradual migration (recommended)</li>
<li>New project setup</li>
</ul>
</li>
<li><strong>Error Handling Migration</strong>
<ul>
<li>Before and after examples</li>
<li>Migration helper utilities</li>
</ul>
</li>
<li><strong>IHttpClientFactory Migration</strong>
<ul>
<li>Manual vs. DI setup</li>
</ul>
</li>
<li><strong>Logging Integration</strong>
<ul>
<li>Setup and configuration</li>
</ul>
</li>
<li><strong>Performance Improvements</strong>
<ul>
<li>Connection pooling</li>
<li>Retry policies</li>
<li>Rate limit optimization</li>
</ul>
</li>
<li><strong>Best Practices Updates</strong>
<ul>
<li>Cancellation tokens</li>
<li>Error handling patterns</li>
<li>Dependency injection</li>
<li>Caching strategies</li>
</ul>
</li>
<li><strong>Troubleshooting Common Issues</strong>
<ul>
<li>Null reference exceptions</li>
<li>HttpClient disposal issues</li>
<li>Rate limit handling</li>
<li>Configuration problems</li>
</ul>
</li>
<li><strong>Migration Checklist</strong></li>
<li><strong>Testing Your Migration</strong></li>
</ul>
<h3 id="-error-handling-guide">⚠️ <a href="error-handling.html">Error Handling Guide</a></h3>
<ul>
<li><strong>Exception Types</strong>
<ul>
<li>UnsplasharpException (base)</li>
<li>UnsplasharpNotFoundException</li>
<li>UnsplasharpRateLimitException</li>
<li>UnsplasharpAuthenticationException</li>
<li>UnsplasharpNetworkException</li>
<li>UnsplasharpTimeoutException</li>
</ul>
</li>
<li><strong>Error Context</strong>
<ul>
<li>Rich error information</li>
<li>Correlation IDs</li>
<li>Request/response details</li>
</ul>
</li>
<li><strong>Handling Strategies</strong>
<ul>
<li>Basic error handling</li>
<li>Advanced error handling</li>
<li>Retry patterns</li>
</ul>
</li>
<li><strong>Rate Limit Handling</strong>
<ul>
<li>Detection and response</li>
<li>Backoff strategies</li>
</ul>
</li>
<li><strong>Network Error Handling</strong>
<ul>
<li>Transient vs. permanent errors</li>
<li>Retry logic</li>
</ul>
</li>
<li><strong>Authentication Errors</strong>
<ul>
<li>API key validation</li>
<li>Permission issues</li>
</ul>
</li>
<li><strong>Best Practices</strong>
<ul>
<li>Exception-throwing methods</li>
<li>Logging strategies</li>
<li>User experience considerations</li>
</ul>
</li>
</ul>
<h3 id="-ihttpclientfactory-integration">🌐 <a href="http-client-factory.html">IHttpClientFactory Integration</a></h3>
<ul>
<li><strong>Overview and Benefits</strong></li>
<li><strong>Basic Setup</strong></li>
<li><strong>Advanced Configuration</strong></li>
<li><strong>Dependency Injection</strong></li>
<li><strong>Custom HTTP Handlers</strong></li>
<li><strong>Performance Considerations</strong></li>
<li><strong>Troubleshooting</strong></li>
</ul>
<h3 id="-logging-configuration">📝 <a href="logging.html">Logging Configuration</a></h3>
<ul>
<li><strong>Setup and Configuration</strong></li>
<li><strong>Log Levels and Categories</strong></li>
<li><strong>Structured Logging</strong></li>
<li><strong>Custom Log Providers</strong></li>
<li><strong>Performance Considerations</strong></li>
<li><strong>Troubleshooting</strong></li>
</ul>
<h3 id="-introduction">📖 <a href="docs/introduction.html">Introduction</a></h3>
<ul>
<li>Library overview</li>
<li>Key features</li>
<li>Architecture</li>
<li>Getting started</li>
</ul>
<h3 id="-obtaining-an-api-key">🔑 <a href="docs/obtaining-an-api-key.html">Obtaining an API Key</a></h3>
<ul>
<li>Unsplash developer account setup</li>
<li>Application registration</li>
<li>API key management</li>
<li>Rate limits and quotas</li>
</ul>
<h3 id="-downloading-a-photo">📥 <a href="docs/downloading-a-photo.html">Downloading a Photo</a></h3>
<ul>
<li>Basic download examples</li>
<li>Different image sizes</li>
<li>Error handling</li>
<li>Best practices</li>
</ul>
<h3 id="-navigation-guide">🧭 <a href="navigation.html">Navigation Guide</a></h3>
<ul>
<li>Find what you need quickly</li>
<li>Documentation structure</li>
<li>Learning paths</li>
<li>Search tips</li>
</ul>
<h2 id="-documentation-categories">📚 Documentation Categories</h2>
<h3 id="by-audience">By Audience</h3>
<ul>
<li><strong>Beginners</strong>: Getting Started, Introduction, API Key Setup</li>
<li><strong>Developers</strong>: API Reference, Models, Code Examples</li>
<li><strong>Advanced Users</strong>: Advanced Usage, Testing, Migration</li>
<li><strong>DevOps/Production</strong>: Best Practices, Error Handling, Monitoring</li>
</ul>
<h3 id="by-topic">By Topic</h3>
<ul>
<li><strong>Setup &amp; Configuration</strong>: Getting Started, HTTP Client Factory, Logging</li>
<li><strong>API Usage</strong>: API Reference, Models, Error Handling</li>
<li><strong>Development</strong>: Code Examples, Testing, Best Practices</li>
<li><strong>Advanced Topics</strong>: Advanced Usage, Performance, Migration</li>
<li><strong>Production</strong>: Testing, Security, Monitoring, Deployment</li>
</ul>
<h3 id="by-content-type">By Content Type</h3>
<ul>
<li><strong>Guides</strong>: Step-by-step instructions and tutorials</li>
<li><strong>References</strong>: Complete API and model documentation</li>
<li><strong>Examples</strong>: Practical code samples and recipes</li>
<li><strong>Best Practices</strong>: Recommendations and patterns</li>
</ul>
<h2 id="-cross-references">🔗 Cross-References</h2>
<h3 id="common-workflows">Common Workflows</h3>
<ol>
<li><strong>New Project Setup</strong>: Getting Started → API Key → Basic Examples → Error Handling</li>
<li><strong>Production Deployment</strong>: Best Practices → Testing → Error Handling → Monitoring</li>
<li><strong>Performance Optimization</strong>: Advanced Usage → Caching → Batch Operations → Monitoring</li>
<li><strong>Troubleshooting</strong>: Error Handling → Testing → Migration Guide → Navigation</li>
</ol>
<h3 id="related-topics">Related Topics</h3>
<ul>
<li><strong>Error Handling</strong> ↔ <strong>Testing</strong> ↔ <strong>Best Practices</strong></li>
<li><strong>API Reference</strong> ↔ <strong>Models</strong> ↔ <strong>Code Examples</strong></li>
<li><strong>Advanced Usage</strong> ↔ <strong>Performance</strong> ↔ <strong>Production</strong></li>
<li><strong>Migration</strong> ↔ <strong>Best Practices</strong> ↔ <strong>Testing</strong></li>
</ul>
<hr>
<p><strong>Quick Navigation</strong>: <a href="index.html">Main Hub</a> | <a href="docs/getting-started.html">Getting Started</a> | <a href="api-reference.html">API Reference</a> | <a href="code-examples.html">Examples</a></p>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/rootasjey/unsplasharp/blob/master/docs/table-of-contents.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
