<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Testing and Best Practices Guide | Unsplasharp Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Testing and Best Practices Guide | Unsplasharp Documentation ">
      
      
      <link rel="icon" href="favicon.ico">
      <link rel="stylesheet" href="public/docfx.min.css">
      <link rel="stylesheet" href="public/main.css">
      <meta name="docfx:navrel" content="toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="">
      
      
      <meta name="docfx:docurl" content="https://github.com/rootasjey/unsplasharp/blob/master/docs/testing-best-practices.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="index.html">
            <img id="logo" class="svg" src="logo.svg" alt="Unsplasharp">
            Unsplasharp
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">

      <div class="content">
        <div class="actionbar">

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="testing-and-best-practices-guide">Testing and Best Practices Guide</h1>

<p>This comprehensive guide covers testing strategies, best practices, and common pitfalls to avoid when working with Unsplasharp.</p>
<h2 id="table-of-contents">Table of Contents</h2>
<ul>
<li><a href="#testing-strategies">Testing Strategies</a></li>
<li><a href="#unit-testing">Unit Testing</a></li>
<li><a href="#integration-testing">Integration Testing</a></li>
<li><a href="#performance-testing">Performance Testing</a></li>
<li><a href="#best-practices">Best Practices</a></li>
<li><a href="#common-pitfalls">Common Pitfalls</a></li>
<li><a href="#security-considerations">Security Considerations</a></li>
<li><a href="#production-deployment">Production Deployment</a></li>
<li><a href="#monitoring-and-observability">Monitoring and Observability</a></li>
</ul>
<h2 id="testing-strategies">Testing Strategies</h2>
<h3 id="test-pyramid-for-unsplasharp-applications">Test Pyramid for Unsplasharp Applications</h3>
<pre><code>    /\
   /  \     E2E Tests (Few)
  /____\    - Full application flow
 /      \   - Real API integration
/__________\ Integration Tests (Some)
            - API contract testing
            - Error handling scenarios
            
Unit Tests (Many)
- Business logic
- Error handling
- Caching behavior
- Data transformation
</code></pre>
<h3 id="testing-approach">Testing Approach</h3>
<ol>
<li><strong>Unit Tests (70%)</strong>: Test business logic, error handling, and data transformations</li>
<li><strong>Integration Tests (20%)</strong>: Test API interactions and error scenarios</li>
<li><strong>End-to-End Tests (10%)</strong>: Test complete user workflows</li>
</ol>
<h2 id="unit-testing">Unit Testing</h2>
<h3 id="setting-up-test-infrastructure">Setting Up Test Infrastructure</h3>
<pre><code class="lang-csharp">[TestFixture]
public class PhotoServiceTests
{
    private Mock&lt;UnsplasharpClient&gt; _mockClient;
    private Mock&lt;IMemoryCache&gt; _mockCache;
    private Mock&lt;ILogger&lt;PhotoService&gt;&gt; _mockLogger;
    private PhotoService _photoService;
    private TestData _testData;

    [SetUp]
    public void Setup()
    {
        _mockClient = new Mock&lt;UnsplasharpClient&gt;(&quot;test-app-id&quot;);
        _mockCache = new Mock&lt;IMemoryCache&gt;();
        _mockLogger = new Mock&lt;ILogger&lt;PhotoService&gt;&gt;();
        _testData = new TestData();
        
        _photoService = new PhotoService(_mockClient.Object, _mockCache.Object, _mockLogger.Object);
    }

    [TearDown]
    public void TearDown()
    {
        _photoService?.Dispose();
    }
}

public class TestData
{
    public Photo CreateValidPhoto(string id = null)
    {
        return new Photo
        {
            Id = id ?? Guid.NewGuid().ToString(),
            Description = &quot;Test photo description&quot;,
            Width = 1920,
            Height = 1080,
            Color = &quot;#FF5733&quot;,
            Likes = 150,
            Downloads = 500,
            CreatedAt = DateTime.UtcNow.AddDays(-30).ToString(&quot;O&quot;),
            UpdatedAt = DateTime.UtcNow.AddDays(-1).ToString(&quot;O&quot;),
            User = CreateValidUser(),
            Urls = CreateValidUrls(),
            Exif = CreateValidExif(),
            Location = CreateValidLocation()
        };
    }

    public User CreateValidUser(string username = null)
    {
        return new User
        {
            Id = Guid.NewGuid().ToString(),
            Username = username ?? &quot;testuser&quot;,
            Name = &quot;Test User&quot;,
            Bio = &quot;Test photographer&quot;,
            TotalPhotos = 100,
            TotalLikes = 5000,
            ProfileImage = new ProfileImage
            {
                Small = &quot;https://example.com/profile-small.jpg&quot;,
                Medium = &quot;https://example.com/profile-medium.jpg&quot;,
                Large = &quot;https://example.com/profile-large.jpg&quot;
            }
        };
    }

    public Urls CreateValidUrls()
    {
        return new Urls
        {
            Thumbnail = &quot;https://example.com/thumb.jpg&quot;,
            Small = &quot;https://example.com/small.jpg&quot;,
            Regular = &quot;https://example.com/regular.jpg&quot;,
            Full = &quot;https://example.com/full.jpg&quot;,
            Raw = &quot;https://example.com/raw.jpg&quot;
        };
    }

    public Exif CreateValidExif()
    {
        return new Exif
        {
            Make = &quot;Canon&quot;,
            Model = &quot;EOS 5D Mark IV&quot;,
            Aperture = &quot;2.8&quot;,
            ExposureTime = &quot;1/125&quot;,
            Iso = 400,
            FocalLength = &quot;85mm&quot;
        };
    }

    public Location CreateValidLocation()
    {
        return new Location
        {
            Name = &quot;Paris, France&quot;,
            City = &quot;Paris&quot;,
            Country = &quot;France&quot;,
            Position = new Position
            {
                Latitude = 48.8566,
                Longitude = 2.3522
            }
        };
    }
}
</code></pre>
<h3 id="testing-success-scenarios">Testing Success Scenarios</h3>
<pre><code class="lang-csharp">[Test]
public async Task GetRandomPhoto_Success_ReturnsPhoto()
{
    // Arrange
    var expectedPhoto = _testData.CreateValidPhoto();
    _mockClient.Setup(c =&gt; c.GetRandomPhotoAsync(It.IsAny&lt;CancellationToken&gt;()))
              .ReturnsAsync(expectedPhoto);

    // Act
    var result = await _photoService.GetRandomPhotoAsync();

    // Assert
    Assert.IsNotNull(result);
    Assert.AreEqual(expectedPhoto.Id, result.Id);
    Assert.AreEqual(expectedPhoto.Description, result.Description);
    Assert.AreEqual(expectedPhoto.User.Name, result.User.Name);
    
    // Verify the mock was called correctly
    _mockClient.Verify(c =&gt; c.GetRandomPhotoAsync(It.IsAny&lt;CancellationToken&gt;()), Times.Once);
}

[Test]
public async Task SearchPhotos_WithValidQuery_ReturnsFilteredResults()
{
    // Arrange
    var query = &quot;nature&quot;;
    var photos = new List&lt;Photo&gt;
    {
        _testData.CreateValidPhoto(&quot;1&quot;),
        _testData.CreateValidPhoto(&quot;2&quot;),
        _testData.CreateValidPhoto(&quot;3&quot;)
    };
    
    _mockClient.Setup(c =&gt; c.SearchPhotosAsync(
        query, 
        It.IsAny&lt;int&gt;(), 
        It.IsAny&lt;int&gt;(), 
        It.IsAny&lt;OrderBy&gt;(),
        It.IsAny&lt;string&gt;(),
        It.IsAny&lt;string&gt;(),
        It.IsAny&lt;string&gt;(),
        It.IsAny&lt;Orientation&gt;(),
        It.IsAny&lt;CancellationToken&gt;()))
        .ReturnsAsync(photos);

    // Act
    var result = await _photoService.SearchPhotosAsync(query);

    // Assert
    Assert.IsNotNull(result);
    Assert.AreEqual(photos.Count, result.Count);
    CollectionAssert.AreEqual(photos.Select(p =&gt; p.Id), result.Select(p =&gt; p.Id));
}
</code></pre>
<h3 id="testing-error-scenarios">Testing Error Scenarios</h3>
<pre><code class="lang-csharp">[Test]
public async Task GetPhoto_NotFound_ReturnsNull()
{
    // Arrange
    var photoId = &quot;non-existent-id&quot;;
    _mockClient.Setup(c =&gt; c.GetPhotoAsync(photoId, It.IsAny&lt;CancellationToken&gt;()))
              .ThrowsAsync(new UnsplasharpNotFoundException(&quot;Photo not found&quot;, photoId, &quot;Photo&quot;, &quot;https://api.unsplash.com/photos/non-existent-id&quot;, &quot;GET&quot;, new ErrorContext(&quot;test-app&quot;)));

    // Act
    var result = await _photoService.GetPhotoSafelyAsync(photoId);

    // Assert
    Assert.IsNull(result);
    
    // Verify logging
    VerifyLogCalled(LogLevel.Warning, &quot;Photo non-existent-id not found&quot;);
}

[Test]
public async Task GetPhoto_RateLimited_ThrowsException()
{
    // Arrange
    var photoId = &quot;test-id&quot;;
    var rateLimitException = new UnsplasharpRateLimitException(
        &quot;Rate limit exceeded&quot;, 
        0, 5000, 
        DateTimeOffset.UtcNow.AddMinutes(15),
        &quot;https://api.unsplash.com/photos/test-id&quot;, 
        &quot;GET&quot;, 
        new ErrorContext(&quot;test-app&quot;));
    
    _mockClient.Setup(c =&gt; c.GetPhotoAsync(photoId, It.IsAny&lt;CancellationToken&gt;()))
              .ThrowsAsync(rateLimitException);

    // Act &amp; Assert
    var ex = await Assert.ThrowsAsync&lt;UnsplasharpRateLimitException&gt;(
        () =&gt; _photoService.GetPhotoAsync(photoId));
    
    Assert.AreEqual(0, ex.RateLimitRemaining);
    Assert.AreEqual(5000, ex.RateLimit);
    Assert.IsNotNull(ex.RateLimitReset);
}

[Test]
public async Task SearchPhotos_NetworkError_RetriesAndFails()
{
    // Arrange
    var query = &quot;test&quot;;
    var networkException = new UnsplasharpNetworkException(
        &quot;Network error&quot;, 
        new HttpRequestException(&quot;Connection failed&quot;),
        true, // IsRetryable
        &quot;https://api.unsplash.com/search/photos&quot;,
        &quot;GET&quot;,
        new ErrorContext(&quot;test-app&quot;));
    
    _mockClient.Setup(c =&gt; c.SearchPhotosAsync(
        It.IsAny&lt;string&gt;(), 
        It.IsAny&lt;int&gt;(), 
        It.IsAny&lt;int&gt;(), 
        It.IsAny&lt;OrderBy&gt;(),
        It.IsAny&lt;string&gt;(),
        It.IsAny&lt;string&gt;(),
        It.IsAny&lt;string&gt;(),
        It.IsAny&lt;Orientation&gt;(),
        It.IsAny&lt;CancellationToken&gt;()))
        .ThrowsAsync(networkException);

    // Act &amp; Assert
    var ex = await Assert.ThrowsAsync&lt;UnsplasharpNetworkException&gt;(
        () =&gt; _photoService.SearchPhotosAsync(query));
    
    Assert.IsTrue(ex.IsRetryable);
    
    // Verify retry attempts (if implemented)
    _mockClient.Verify(c =&gt; c.SearchPhotosAsync(
        It.IsAny&lt;string&gt;(), 
        It.IsAny&lt;int&gt;(), 
        It.IsAny&lt;int&gt;(), 
        It.IsAny&lt;OrderBy&gt;(),
        It.IsAny&lt;string&gt;(),
        It.IsAny&lt;string&gt;(),
        It.IsAny&lt;string&gt;(),
        It.IsAny&lt;Orientation&gt;(),
        It.IsAny&lt;CancellationToken&gt;()), 
        Times.AtLeastOnce);
}

private void VerifyLogCalled(LogLevel level, string message)
{
    _mockLogger.Verify(
        x =&gt; x.Log(
            level,
            It.IsAny&lt;EventId&gt;(),
            It.Is&lt;It.IsAnyType&gt;((v, t) =&gt; v.ToString().Contains(message)),
            It.IsAny&lt;Exception&gt;(),
            It.IsAny&lt;Func&lt;It.IsAnyType, Exception, string&gt;&gt;()),
        Times.Once);
}
</code></pre>
<h3 id="testing-caching-behavior">Testing Caching Behavior</h3>
<pre><code class="lang-csharp">[Test]
public async Task GetPhoto_CacheHit_ReturnsFromCache()
{
    // Arrange
    var photoId = &quot;cached-photo&quot;;
    var cachedPhoto = _testData.CreateValidPhoto(photoId);
    
    object cacheValue = cachedPhoto;
    _mockCache.Setup(c =&gt; c.TryGetValue($&quot;photo:{photoId}&quot;, out cacheValue))
              .Returns(true);

    // Act
    var result = await _photoService.GetPhotoAsync(photoId);

    // Assert
    Assert.IsNotNull(result);
    Assert.AreEqual(cachedPhoto.Id, result.Id);
    
    // Verify API was not called
    _mockClient.Verify(c =&gt; c.GetPhotoAsync(It.IsAny&lt;string&gt;(), It.IsAny&lt;CancellationToken&gt;()), Times.Never);
    
    // Verify cache was checked
    _mockCache.Verify(c =&gt; c.TryGetValue($&quot;photo:{photoId}&quot;, out It.Ref&lt;object&gt;.IsAny), Times.Once);
}

[Test]
public async Task GetPhoto_CacheMiss_FetchesAndCaches()
{
    // Arrange
    var photoId = &quot;new-photo&quot;;
    var photo = _testData.CreateValidPhoto(photoId);
    
    object cacheValue = null;
    _mockCache.Setup(c =&gt; c.TryGetValue($&quot;photo:{photoId}&quot;, out cacheValue))
              .Returns(false);
    
    _mockClient.Setup(c =&gt; c.GetPhotoAsync(photoId, It.IsAny&lt;CancellationToken&gt;()))
              .ReturnsAsync(photo);

    // Act
    var result = await _photoService.GetPhotoAsync(photoId);

    // Assert
    Assert.IsNotNull(result);
    Assert.AreEqual(photo.Id, result.Id);
    
    // Verify API was called
    _mockClient.Verify(c =&gt; c.GetPhotoAsync(photoId, It.IsAny&lt;CancellationToken&gt;()), Times.Once);
    
    // Verify caching occurred
    _mockCache.Verify(c =&gt; c.Set(
        $&quot;photo:{photoId}&quot;, 
        photo, 
        It.IsAny&lt;TimeSpan&gt;()), Times.Once);
}
</code></pre>
<h2 id="integration-testing">Integration Testing</h2>
<h3 id="test-configuration">Test Configuration</h3>
<pre><code class="lang-csharp">[TestFixture]
[Category(&quot;Integration&quot;)]
public class UnsplashIntegrationTests
{
    private UnsplasharpClient _client;
    private ILogger&lt;UnsplashIntegrationTests&gt; _logger;
    private readonly string _testApplicationId;

    public UnsplashIntegrationTests()
    {
        // Use environment variable or test configuration
        _testApplicationId = Environment.GetEnvironmentVariable(&quot;UNSPLASH_TEST_APP_ID&quot;)
                           ?? throw new InvalidOperationException(&quot;UNSPLASH_TEST_APP_ID environment variable not set&quot;);
    }

    [SetUp]
    public void Setup()
    {
        var loggerFactory = LoggerFactory.Create(builder =&gt;
            builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        _logger = loggerFactory.CreateLogger&lt;UnsplashIntegrationTests&gt;();

        _client = new UnsplasharpClient(_testApplicationId, logger: _logger);
    }

    [TearDown]
    public void TearDown()
    {
        _client?.Dispose();
    }
}
</code></pre>
<h3 id="api-contract-testing">API Contract Testing</h3>
<pre><code class="lang-csharp">[Test]
[Retry(3)] // Retry on rate limit or network issues
public async Task GetRandomPhoto_ReturnsValidPhotoStructure()
{
    // Act
    var photo = await _client.GetRandomPhotoAsync();

    // Assert - Validate complete photo structure
    Assert.IsNotNull(photo);

    // Basic properties
    Assert.IsNotEmpty(photo.Id);
    Assert.Greater(photo.Width, 0);
    Assert.Greater(photo.Height, 0);
    Assert.IsNotEmpty(photo.Color);
    Assert.GreaterOrEqual(photo.Likes, 0);
    Assert.GreaterOrEqual(photo.Downloads, 0);

    // User information
    Assert.IsNotNull(photo.User);
    Assert.IsNotEmpty(photo.User.Id);
    Assert.IsNotEmpty(photo.User.Name);
    Assert.IsNotEmpty(photo.User.Username);

    // URLs
    Assert.IsNotNull(photo.Urls);
    Assert.IsTrue(Uri.IsWellFormedUriString(photo.Urls.Thumbnail, UriKind.Absolute));
    Assert.IsTrue(Uri.IsWellFormedUriString(photo.Urls.Small, UriKind.Absolute));
    Assert.IsTrue(Uri.IsWellFormedUriString(photo.Urls.Regular, UriKind.Absolute));
    Assert.IsTrue(Uri.IsWellFormedUriString(photo.Urls.Full, UriKind.Absolute));
    Assert.IsTrue(Uri.IsWellFormedUriString(photo.Urls.Raw, UriKind.Absolute));

    // Timestamps
    Assert.DoesNotThrow(() =&gt; DateTime.Parse(photo.CreatedAt));
    Assert.DoesNotThrow(() =&gt; DateTime.Parse(photo.UpdatedAt));

    _logger.LogInformation(&quot;Successfully validated photo structure for {PhotoId}&quot;, photo.Id);
}

[Test]
public async Task SearchPhotos_WithValidQuery_ReturnsRelevantResults()
{
    // Arrange
    var query = &quot;nature&quot;;
    var expectedMinResults = 5;

    // Act
    var photos = await _client.SearchPhotosAsync(query, perPage: 10);

    // Assert
    Assert.IsNotNull(photos);
    Assert.GreaterOrEqual(photos.Count, expectedMinResults);
    Assert.Greater(_client.LastPhotosSearchTotalResults, 0);
    Assert.Greater(_client.LastPhotosSearchTotalPages, 0);

    // Validate each photo
    foreach (var photo in photos)
    {
        Assert.IsNotEmpty(photo.Id);
        Assert.IsNotNull(photo.User);
        Assert.IsNotNull(photo.Urls);

        // Check if description or tags might contain the search term
        var searchRelevant = photo.Description?.ToLowerInvariant().Contains(query.ToLowerInvariant()) == true ||
                           photo.Categories.Any(c =&gt; c.Title.ToLowerInvariant().Contains(query.ToLowerInvariant()));

        // Note: Not all results may contain the exact term due to Unsplash's search algorithm
        _logger.LogDebug(&quot;Photo {PhotoId}: {Description} - Search relevant: {Relevant}&quot;,
            photo.Id, photo.Description, searchRelevant);
    }
}

[Test]
public async Task GetPhoto_WithInvalidId_ThrowsNotFoundException()
{
    // Arrange
    var invalidId = &quot;definitely-not-a-real-photo-id-12345&quot;;

    // Act &amp; Assert
    var ex = await Assert.ThrowsAsync&lt;UnsplasharpNotFoundException&gt;(
        () =&gt; _client.GetPhotoAsync(invalidId));

    Assert.AreEqual(invalidId, ex.ResourceId);
    Assert.AreEqual(&quot;Photo&quot;, ex.ResourceType);
    Assert.IsNotNull(ex.Context);
    Assert.IsNotEmpty(ex.Context.CorrelationId);

    _logger.LogInformation(&quot;Correctly handled not found exception for {PhotoId}&quot;, invalidId);
}
</code></pre>
<h3 id="rate-limit-testing">Rate Limit Testing</h3>
<pre><code class="lang-csharp">[Test]
[Explicit(&quot;Only run when testing rate limits&quot;)]
public async Task RateLimit_ExceedsLimit_HandlesGracefully()
{
    var successCount = 0;
    var rateLimitCount = 0;
    var tasks = new List&lt;Task&gt;();

    // Create many concurrent requests to trigger rate limiting
    for (int i = 0; i &lt; 100; i++)
    {
        tasks.Add(Task.Run(async () =&gt;
        {
            try
            {
                await _client.GetRandomPhotoAsync();
                Interlocked.Increment(ref successCount);
            }
            catch (UnsplasharpRateLimitException ex)
            {
                Interlocked.Increment(ref rateLimitCount);
                _logger.LogWarning(&quot;Rate limited: {Remaining}/{Total}, Reset: {Reset}&quot;,
                    ex.RateLimitRemaining, ex.RateLimit, ex.RateLimitReset);
            }
        }));
    }

    await Task.WhenAll(tasks);

    _logger.LogInformation(&quot;Rate limit test completed: {Success} successful, {RateLimited} rate limited&quot;,
        successCount, rateLimitCount);

    Assert.Greater(successCount, 0, &quot;At least some requests should succeed&quot;);
    Assert.Greater(rateLimitCount, 0, &quot;Should hit rate limits with 100 concurrent requests&quot;);
}

[Test]
public async Task RateLimit_CheckHeaders_UpdatesClientState()
{
    // Act
    await _client.GetRandomPhotoAsync();

    // Assert
    Assert.Greater(_client.MaxRateLimit, 0);
    Assert.GreaterOrEqual(_client.RateLimitRemaining, 0);
    Assert.LessOrEqual(_client.RateLimitRemaining, _client.MaxRateLimit);

    _logger.LogInformation(&quot;Rate limit status: {Remaining}/{Max}&quot;,
        _client.RateLimitRemaining, _client.MaxRateLimit);
}
</code></pre>
<h2 id="performance-testing">Performance Testing</h2>
<h3 id="load-testing">Load Testing</h3>
<pre><code class="lang-csharp">[TestFixture]
[Category(&quot;Performance&quot;)]
public class PerformanceTests
{
    private UnsplasharpClient _client;
    private ILogger&lt;PerformanceTests&gt; _logger;

    [SetUp]
    public void Setup()
    {
        var loggerFactory = LoggerFactory.Create(builder =&gt;
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        _logger = loggerFactory.CreateLogger&lt;PerformanceTests&gt;();

        _client = new UnsplasharpClient(Environment.GetEnvironmentVariable(&quot;UNSPLASH_TEST_APP_ID&quot;));
    }

    [Test]
    [Explicit(&quot;Performance test - run manually&quot;)]
    public async Task Performance_ConcurrentRequests_MeasuresThroughput()
    {
        const int concurrentRequests = 10;
        const int requestsPerWorker = 5;

        var stopwatch = Stopwatch.StartNew();
        var successCount = 0;
        var errorCount = 0;

        var tasks = Enumerable.Range(0, concurrentRequests).Select(async workerId =&gt;
        {
            for (int i = 0; i &lt; requestsPerWorker; i++)
            {
                try
                {
                    await _client.GetRandomPhotoAsync();
                    Interlocked.Increment(ref successCount);
                }
                catch (UnsplasharpRateLimitException)
                {
                    // Expected under load
                    Interlocked.Increment(ref errorCount);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, &quot;Unexpected error in worker {WorkerId}, request {RequestId}&quot;, workerId, i);
                    Interlocked.Increment(ref errorCount);
                }

                // Small delay to avoid overwhelming the API
                await Task.Delay(100);
            }
        });

        await Task.WhenAll(tasks);
        stopwatch.Stop();

        var totalRequests = successCount + errorCount;
        var throughput = totalRequests / stopwatch.Elapsed.TotalSeconds;

        _logger.LogInformation(&quot;Performance test completed:&quot;);
        _logger.LogInformation(&quot;  Total requests: {Total}&quot;, totalRequests);
        _logger.LogInformation(&quot;  Successful: {Success}&quot;, successCount);
        _logger.LogInformation(&quot;  Errors: {Errors}&quot;, errorCount);
        _logger.LogInformation(&quot;  Duration: {Duration:F2}s&quot;, stopwatch.Elapsed.TotalSeconds);
        _logger.LogInformation(&quot;  Throughput: {Throughput:F2} req/s&quot;, throughput);

        Assert.Greater(successCount, 0);
        Assert.Greater(throughput, 0.5); // At least 0.5 requests per second
    }

    [Test]
    public async Task Performance_ResponseTime_WithinAcceptableLimits()
    {
        const int iterations = 10;
        var responseTimes = new List&lt;TimeSpan&gt;();

        for (int i = 0; i &lt; iterations; i++)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                await _client.GetRandomPhotoAsync();
                stopwatch.Stop();
                responseTimes.Add(stopwatch.Elapsed);
            }
            catch (UnsplasharpRateLimitException)
            {
                // Skip rate limited requests
                continue;
            }

            // Small delay between requests
            await Task.Delay(200);
        }

        if (responseTimes.Count == 0)
        {
            Assert.Inconclusive(&quot;All requests were rate limited&quot;);
        }

        var averageResponseTime = TimeSpan.FromMilliseconds(responseTimes.Average(t =&gt; t.TotalMilliseconds));
        var maxResponseTime = responseTimes.Max();

        _logger.LogInformation(&quot;Response time analysis:&quot;);
        _logger.LogInformation(&quot;  Average: {Average:F0}ms&quot;, averageResponseTime.TotalMilliseconds);
        _logger.LogInformation(&quot;  Maximum: {Max:F0}ms&quot;, maxResponseTime.TotalMilliseconds);
        _logger.LogInformation(&quot;  Samples: {Count}&quot;, responseTimes.Count);

        Assert.Less(averageResponseTime.TotalSeconds, 5.0, &quot;Average response time should be under 5 seconds&quot;);
        Assert.Less(maxResponseTime.TotalSeconds, 10.0, &quot;Maximum response time should be under 10 seconds&quot;);
    }
}
</code></pre>
<h3 id="memory-usage-testing">Memory Usage Testing</h3>
<pre><code class="lang-csharp">[Test]
[Explicit(&quot;Memory test - run manually&quot;)]
public async Task Memory_MultipleRequests_NoMemoryLeaks()
{
    const int iterations = 100;

    // Force garbage collection before test
    GC.Collect();
    GC.WaitForPendingFinalizers();
    GC.Collect();

    var initialMemory = GC.GetTotalMemory(false);

    for (int i = 0; i &lt; iterations; i++)
    {
        try
        {
            var photo = await _client.GetRandomPhotoAsync();

            // Process the photo to ensure it's not optimized away
            var info = $&quot;{photo.Id}:{photo.Width}x{photo.Height}&quot;;

            if (i % 10 == 0)
            {
                var currentMemory = GC.GetTotalMemory(false);
                _logger.LogDebug(&quot;Iteration {Iteration}: Memory usage {Memory:N0} bytes&quot;, i, currentMemory);
            }
        }
        catch (UnsplasharpRateLimitException)
        {
            // Wait and continue
            await Task.Delay(1000);
        }
    }

    // Force garbage collection after test
    GC.Collect();
    GC.WaitForPendingFinalizers();
    GC.Collect();

    var finalMemory = GC.GetTotalMemory(false);
    var memoryIncrease = finalMemory - initialMemory;

    _logger.LogInformation(&quot;Memory usage analysis:&quot;);
    _logger.LogInformation(&quot;  Initial: {Initial:N0} bytes&quot;, initialMemory);
    _logger.LogInformation(&quot;  Final: {Final:N0} bytes&quot;, finalMemory);
    _logger.LogInformation(&quot;  Increase: {Increase:N0} bytes&quot;, memoryIncrease);

    // Allow for some memory increase, but not excessive
    var maxAllowedIncrease = 10 * 1024 * 1024; // 10MB
    Assert.Less(memoryIncrease, maxAllowedIncrease,
        $&quot;Memory increase should be less than {maxAllowedIncrease:N0} bytes&quot;);
}
</code></pre>
<h2 id="best-practices">Best Practices</h2>
<h3 id="1-error-handling-best-practices">1. Error Handling Best Practices</h3>
<pre><code class="lang-csharp">// ✅ Good: Specific exception handling
public async Task&lt;Photo?&gt; GetPhotoSafely(string photoId)
{
    try
    {
        return await _client.GetPhotoAsync(photoId);
    }
    catch (UnsplasharpNotFoundException)
    {
        _logger.LogWarning(&quot;Photo {PhotoId} not found&quot;, photoId);
        return null;
    }
    catch (UnsplasharpRateLimitException ex)
    {
        _logger.LogWarning(&quot;Rate limited for photo {PhotoId}. Reset: {Reset}&quot;, photoId, ex.RateLimitReset);
        throw; // Re-throw to let caller handle rate limiting
    }
    catch (UnsplasharpAuthenticationException ex)
    {
        _logger.LogError(ex, &quot;Authentication failed - check API key&quot;);
        throw; // Critical error - should not continue
    }
    catch (UnsplasharpNetworkException ex) when (ex.IsRetryable)
    {
        _logger.LogWarning(&quot;Retryable network error for photo {PhotoId}: {Message}&quot;, photoId, ex.Message);
        throw; // Let retry logic handle this
    }
    catch (UnsplasharpException ex)
    {
        _logger.LogError(ex, &quot;Unexpected Unsplash error for photo {PhotoId}&quot;, photoId);
        throw;
    }
}

// ❌ Bad: Generic exception handling
public async Task&lt;Photo?&gt; GetPhotoBadly(string photoId)
{
    try
    {
        return await _client.GetPhotoAsync(photoId);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, &quot;Error getting photo&quot;);
        return null; // Loses important error information
    }
}
</code></pre>
<h3 id="2-caching-best-practices">2. Caching Best Practices</h3>
<pre><code class="lang-csharp">// ✅ Good: Intelligent caching with appropriate TTL
public class PhotoCacheService
{
    private readonly IMemoryCache _cache;
    private readonly UnsplasharpClient _client;

    public async Task&lt;Photo?&gt; GetPhotoAsync(string photoId)
    {
        var cacheKey = $&quot;photo:{photoId}&quot;;

        if (_cache.TryGetValue(cacheKey, out Photo cachedPhoto))
        {
            return cachedPhoto;
        }

        try
        {
            var photo = await _client.GetPhotoAsync(photoId);

            // Cache popular photos longer
            var cacheDuration = photo.Likes &gt; 1000
                ? TimeSpan.FromHours(2)
                : TimeSpan.FromMinutes(30);

            _cache.Set(cacheKey, photo, cacheDuration);
            return photo;
        }
        catch (UnsplasharpNotFoundException)
        {
            // Cache negative results for shorter time
            _cache.Set(cacheKey, (Photo?)null, TimeSpan.FromMinutes(5));
            return null;
        }
    }
}

// ❌ Bad: No caching or inappropriate caching
public class BadPhotoService
{
    public async Task&lt;Photo?&gt; GetPhotoAsync(string photoId)
    {
        // Always hits API - no caching
        return await _client.GetPhotoAsync(photoId);
    }

    // Or caching everything for the same duration
    public async Task&lt;Photo?&gt; GetPhotoCachedBadly(string photoId)
    {
        var cacheKey = $&quot;photo:{photoId}&quot;;

        if (_cache.TryGetValue(cacheKey, out Photo cachedPhoto))
            return cachedPhoto;

        var photo = await _client.GetPhotoAsync(photoId);

        // Bad: Same cache duration for all photos
        _cache.Set(cacheKey, photo, TimeSpan.FromDays(1)); // Too long!
        return photo;
    }
}
</code></pre>
<h3 id="3-rate-limiting-best-practices">3. Rate Limiting Best Practices</h3>
<pre><code class="lang-csharp">// ✅ Good: Proactive rate limit monitoring
public class RateLimitAwareService
{
    private readonly UnsplasharpClient _client;
    private readonly ILogger&lt;RateLimitAwareService&gt; _logger;

    public async Task&lt;List&lt;Photo&gt;&gt; GetMultiplePhotosAsync(IEnumerable&lt;string&gt; photoIds)
    {
        var photos = new List&lt;Photo&gt;();

        foreach (var photoId in photoIds)
        {
            // Check rate limit before making request
            if (_client.RateLimitRemaining &lt; 10)
            {
                _logger.LogWarning(&quot;Rate limit running low: {Remaining}/{Max}. Pausing requests.&quot;,
                    _client.RateLimitRemaining, _client.MaxRateLimit);

                await Task.Delay(TimeSpan.FromSeconds(30));
            }

            try
            {
                var photo = await _client.GetPhotoAsync(photoId);
                photos.Add(photo);
            }
            catch (UnsplasharpRateLimitException ex)
            {
                _logger.LogWarning(&quot;Rate limited. Waiting until {Reset}&quot;, ex.RateLimitReset);

                if (ex.TimeUntilReset.HasValue)
                {
                    await Task.Delay(ex.TimeUntilReset.Value);

                    // Retry the request
                    var photo = await _client.GetPhotoAsync(photoId);
                    photos.Add(photo);
                }
                break; // Stop processing if rate limited
            }
        }

        return photos;
    }
}

// ❌ Bad: Ignoring rate limits
public class BadRateLimitService
{
    public async Task&lt;List&lt;Photo&gt;&gt; GetMultiplePhotosBadly(IEnumerable&lt;string&gt; photoIds)
    {
        var tasks = photoIds.Select(id =&gt; _client.GetPhotoAsync(id));

        try
        {
            var photos = await Task.WhenAll(tasks);
            return photos.ToList();
        }
        catch (UnsplasharpRateLimitException)
        {
            // Bad: Just give up on rate limit
            return new List&lt;Photo&gt;();
        }
    }
}
</code></pre>
<h3 id="4-dependency-injection-best-practices">4. Dependency Injection Best Practices</h3>
<pre><code class="lang-csharp">// ✅ Good: Proper DI setup
public void ConfigureServices(IServiceCollection services)
{
    // Configure Unsplasharp with proper lifetime
    services.AddUnsplasharp(options =&gt;
    {
        options.ApplicationId = Configuration[&quot;Unsplash:ApplicationId&quot;];
        options.ConfigureHttpClient = client =&gt;
        {
            client.Timeout = TimeSpan.FromSeconds(30);
        };
    });

    // Register services with appropriate lifetimes
    services.AddScoped&lt;IPhotoService, PhotoService&gt;();
    services.AddSingleton&lt;IPhotoCache, PhotoCacheService&gt;();
    services.AddMemoryCache();
}

// ❌ Bad: Manual instantiation in services
public class BadPhotoService
{
    private readonly UnsplasharpClient _client;

    public BadPhotoService()
    {
        // Bad: Creates new client instance, no DI
        _client = new UnsplasharpClient(&quot;hardcoded-app-id&quot;);
    }
}
</code></pre>
<h2 id="common-pitfalls">Common Pitfalls</h2>
<h3 id="1-not-handling-rate-limits">1. Not Handling Rate Limits</h3>
<pre><code class="lang-csharp">// ❌ Common mistake: Ignoring rate limits
public async Task&lt;List&lt;Photo&gt;&gt; SearchManyQueries(string[] queries)
{
    var allPhotos = new List&lt;Photo&gt;();

    foreach (var query in queries)
    {
        // This will likely hit rate limits
        var photos = await _client.SearchPhotosAsync(query, perPage: 30);
        allPhotos.AddRange(photos);
    }

    return allPhotos;
}

// ✅ Better approach: Rate limit aware
public async Task&lt;List&lt;Photo&gt;&gt; SearchManyQueriesSafely(string[] queries)
{
    var allPhotos = new List&lt;Photo&gt;();

    foreach (var query in queries)
    {
        try
        {
            var photos = await _client.SearchPhotosAsync(query, perPage: 30);
            allPhotos.AddRange(photos);

            // Courtesy delay between requests
            await Task.Delay(100);
        }
        catch (UnsplasharpRateLimitException ex)
        {
            _logger.LogWarning(&quot;Rate limited, waiting {Delay}ms&quot;, ex.TimeUntilReset?.TotalMilliseconds);

            if (ex.TimeUntilReset.HasValue)
            {
                await Task.Delay(ex.TimeUntilReset.Value);
            }

            // Optionally retry the failed query
        }
    }

    return allPhotos;
}
</code></pre>
<h3 id="2-improper-httpclient-usage">2. Improper HttpClient Usage</h3>
<pre><code class="lang-csharp">// ❌ Bad: Creating HttpClient instances manually
public class BadDownloadService
{
    public async Task DownloadPhoto(string photoUrl, string filePath)
    {
        // Bad: Creates new HttpClient for each download
        using var httpClient = new HttpClient();
        var imageBytes = await httpClient.GetByteArrayAsync(photoUrl);
        await File.WriteAllBytesAsync(filePath, imageBytes);
    }
}

// ✅ Good: Using IHttpClientFactory
public class GoodDownloadService
{
    private readonly IHttpClientFactory _httpClientFactory;

    public GoodDownloadService(IHttpClientFactory httpClientFactory)
    {
        _httpClientFactory = httpClientFactory;
    }

    public async Task DownloadPhoto(string photoUrl, string filePath)
    {
        using var httpClient = _httpClientFactory.CreateClient();
        var imageBytes = await httpClient.GetByteArrayAsync(photoUrl);
        await File.WriteAllBytesAsync(filePath, imageBytes);
    }
}
</code></pre>
<h3 id="3-not-using-cancellation-tokens">3. Not Using Cancellation Tokens</h3>
<pre><code class="lang-csharp">// ❌ Bad: No cancellation support
public async Task&lt;List&lt;Photo&gt;&gt; SearchPhotosSlowly(string query)
{
    var photos = new List&lt;Photo&gt;();

    for (int page = 1; page &lt;= 10; page++)
    {
        // No way to cancel this long-running operation
        var pagePhotos = await _client.SearchPhotosAsync(query, page: page);
        photos.AddRange(pagePhotos);
        await Task.Delay(1000); // Simulate slow processing
    }

    return photos;
}

// ✅ Good: Cancellation token support
public async Task&lt;List&lt;Photo&gt;&gt; SearchPhotosWithCancellation(string query, CancellationToken cancellationToken)
{
    var photos = new List&lt;Photo&gt;();

    for (int page = 1; page &lt;= 10; page++)
    {
        cancellationToken.ThrowIfCancellationRequested();

        var pagePhotos = await _client.SearchPhotosAsync(query, page: page, cancellationToken: cancellationToken);
        photos.AddRange(pagePhotos);

        await Task.Delay(1000, cancellationToken);
    }

    return photos;
}
</code></pre>
<h2 id="security-considerations">Security Considerations</h2>
<h3 id="1-api-key-management">1. API Key Management</h3>
<pre><code class="lang-csharp">// ✅ Good: Secure API key management
public void ConfigureServices(IServiceCollection services)
{
    // Use configuration system
    var unsplashConfig = Configuration.GetSection(&quot;Unsplash&quot;);
    var applicationId = unsplashConfig[&quot;ApplicationId&quot;]
        ?? throw new InvalidOperationException(&quot;Unsplash ApplicationId not configured&quot;);

    services.AddUnsplasharp(applicationId);
}

// appsettings.json (for development)
{
  &quot;Unsplash&quot;: {
    &quot;ApplicationId&quot;: &quot;your-dev-app-id&quot;
  }
}

// Use Azure Key Vault, AWS Secrets Manager, or similar for production
// ❌ Bad: Hardcoded API keys
public class BadService
{
    private readonly UnsplasharpClient _client = new(&quot;hardcoded-api-key-123&quot;);
}
</code></pre>
<h3 id="2-input-validation">2. Input Validation</h3>
<pre><code class="lang-csharp">// ✅ Good: Input validation
public async Task&lt;List&lt;Photo&gt;&gt; SearchPhotosSecurely(string query, int page = 1, int perPage = 20)
{
    // Validate inputs
    if (string.IsNullOrWhiteSpace(query))
        throw new ArgumentException(&quot;Query cannot be empty&quot;, nameof(query));

    if (query.Length &gt; 100)
        throw new ArgumentException(&quot;Query too long&quot;, nameof(query));

    if (page &lt; 1 || page &gt; 1000)
        throw new ArgumentOutOfRangeException(nameof(page), &quot;Page must be between 1 and 1000&quot;);

    if (perPage &lt; 1 || perPage &gt; 30)
        throw new ArgumentOutOfRangeException(nameof(perPage), &quot;PerPage must be between 1 and 30&quot;);

    // Sanitize query to prevent injection attacks (if logging to external systems)
    var sanitizedQuery = query.Replace('\n', ' ').Replace('\r', ' ');

    return await _client.SearchPhotosAsync(sanitizedQuery, page: page, perPage: perPage);
}
</code></pre>
<h3 id="3-rate-limiting-for-user-facing-applications">3. Rate Limiting for User-Facing Applications</h3>
<pre><code class="lang-csharp">// ✅ Good: Implement client-side rate limiting
public class UserRateLimitedPhotoService
{
    private readonly Dictionary&lt;string, DateTime&gt; _userLastRequest = new();
    private readonly TimeSpan _minRequestInterval = TimeSpan.FromSeconds(1);

    public async Task&lt;List&lt;Photo&gt;&gt; SearchPhotosForUser(string userId, string query)
    {
        // Implement per-user rate limiting
        if (_userLastRequest.TryGetValue(userId, out var lastRequest))
        {
            var timeSinceLastRequest = DateTime.UtcNow - lastRequest;
            if (timeSinceLastRequest &lt; _minRequestInterval)
            {
                var waitTime = _minRequestInterval - timeSinceLastRequest;
                await Task.Delay(waitTime);
            }
        }

        _userLastRequest[userId] = DateTime.UtcNow;

        return await _client.SearchPhotosAsync(query);
    }
}
</code></pre>
<h2 id="production-deployment">Production Deployment</h2>
<h3 id="1-configuration-management">1. Configuration Management</h3>
<pre><code class="lang-csharp">// Production-ready configuration
public class UnsplashConfiguration
{
    public string ApplicationId { get; set; } = string.Empty;
    public string? Secret { get; set; }
    public TimeSpan DefaultTimeout { get; set; } = TimeSpan.FromSeconds(30);
    public int MaxRetries { get; set; } = 3;
    public bool EnableCaching { get; set; } = true;
    public TimeSpan CacheDuration { get; set; } = TimeSpan.FromHours(1);
    public int MaxConcurrentRequests { get; set; } = 5;
}

// Startup configuration
public void ConfigureServices(IServiceCollection services)
{
    var unsplashConfig = Configuration.GetSection(&quot;Unsplash&quot;).Get&lt;UnsplashConfiguration&gt;();

    // Validate configuration
    if (string.IsNullOrEmpty(unsplashConfig?.ApplicationId))
    {
        throw new InvalidOperationException(&quot;Unsplash ApplicationId is required&quot;);
    }

    services.AddSingleton(unsplashConfig);

    services.AddUnsplasharp(options =&gt;
    {
        options.ApplicationId = unsplashConfig.ApplicationId;
        options.Secret = unsplashConfig.Secret;
        options.ConfigureHttpClient = client =&gt;
        {
            client.Timeout = unsplashConfig.DefaultTimeout;
        };
    });

    // Add health checks
    services.AddHealthChecks()
        .AddCheck&lt;UnsplashHealthCheck&gt;(&quot;unsplash&quot;);
}
</code></pre>
<h3 id="2-health-checks">2. Health Checks</h3>
<pre><code class="lang-csharp">public class UnsplashHealthCheck : IHealthCheck
{
    private readonly UnsplasharpClient _client;
    private readonly ILogger&lt;UnsplashHealthCheck&gt; _logger;

    public UnsplashHealthCheck(UnsplasharpClient client, ILogger&lt;UnsplashHealthCheck&gt; logger)
    {
        _client = client;
        _logger = logger;
    }

    public async Task&lt;HealthCheckResult&gt; CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Simple health check - get total stats
            var stats = await _client.GetTotalStatsAsync(cancellationToken);

            if (stats == null)
            {
                return HealthCheckResult.Unhealthy(&quot;Unable to retrieve Unsplash statistics&quot;);
            }

            // Check rate limit status
            var rateLimitStatus = _client.RateLimitRemaining &gt; 10
                ? &quot;Healthy&quot;
                : &quot;Low&quot;;

            var data = new Dictionary&lt;string, object&gt;
            {
                [&quot;rate_limit_remaining&quot;] = _client.RateLimitRemaining,
                [&quot;rate_limit_total&quot;] = _client.MaxRateLimit,
                [&quot;rate_limit_status&quot;] = rateLimitStatus,
                [&quot;total_photos&quot;] = stats.Photos,
                [&quot;last_check&quot;] = DateTime.UtcNow
            };

            return _client.RateLimitRemaining &gt; 0
                ? HealthCheckResult.Healthy(&quot;Unsplash API is accessible&quot;, data)
                : HealthCheckResult.Degraded(&quot;Rate limit exhausted&quot;, data);
        }
        catch (UnsplasharpRateLimitException ex)
        {
            _logger.LogWarning(&quot;Health check rate limited: {Message}&quot;, ex.Message);

            return HealthCheckResult.Degraded(&quot;Rate limited&quot;, new Dictionary&lt;string, object&gt;
            {
                [&quot;rate_limit_reset&quot;] = ex.RateLimitReset,
                [&quot;error&quot;] = ex.Message
            });
        }
        catch (UnsplasharpAuthenticationException ex)
        {
            _logger.LogError(ex, &quot;Authentication failed during health check&quot;);

            return HealthCheckResult.Unhealthy(&quot;Authentication failed&quot;, new Dictionary&lt;string, object&gt;
            {
                [&quot;error&quot;] = ex.Message,
                [&quot;check_api_key&quot;] = true
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, &quot;Health check failed&quot;);

            return HealthCheckResult.Unhealthy(&quot;Health check failed&quot;, new Dictionary&lt;string, object&gt;
            {
                [&quot;error&quot;] = ex.Message,
                [&quot;exception_type&quot;] = ex.GetType().Name
            });
        }
    }
}
</code></pre>
<h3 id="3-graceful-degradation">3. Graceful Degradation</h3>
<pre><code class="lang-csharp">public class ResilientPhotoService
{
    private readonly UnsplasharpClient _client;
    private readonly IMemoryCache _cache;
    private readonly ILogger&lt;ResilientPhotoService&gt; _logger;
    private readonly CircuitBreakerPolicy _circuitBreaker;

    public ResilientPhotoService(
        UnsplasharpClient client,
        IMemoryCache cache,
        ILogger&lt;ResilientPhotoService&gt; logger)
    {
        _client = client;
        _cache = cache;
        _logger = logger;

        // Configure circuit breaker
        _circuitBreaker = Policy
            .Handle&lt;UnsplasharpException&gt;()
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: 5,
                durationOfBreak: TimeSpan.FromMinutes(1),
                onBreak: (ex, duration) =&gt; _logger.LogWarning(&quot;Circuit breaker opened for {Duration}&quot;, duration),
                onReset: () =&gt; _logger.LogInformation(&quot;Circuit breaker reset&quot;));
    }

    public async Task&lt;Photo?&gt; GetPhotoWithFallback(string photoId)
    {
        try
        {
            return await _circuitBreaker.ExecuteAsync(async () =&gt;
            {
                return await _client.GetPhotoAsync(photoId);
            });
        }
        catch (CircuitBreakerOpenException)
        {
            _logger.LogWarning(&quot;Circuit breaker open, using cached fallback for photo {PhotoId}&quot;, photoId);

            // Try to return cached version
            if (_cache.TryGetValue($&quot;photo:{photoId}&quot;, out Photo cachedPhoto))
            {
                return cachedPhoto;
            }

            // Return placeholder or null
            return CreatePlaceholderPhoto(photoId);
        }
        catch (UnsplasharpRateLimitException ex)
        {
            _logger.LogWarning(&quot;Rate limited, using cached fallback for photo {PhotoId}&quot;, photoId);

            if (_cache.TryGetValue($&quot;photo:{photoId}&quot;, out Photo cachedPhoto))
            {
                return cachedPhoto;
            }

            return null;
        }
    }

    private Photo CreatePlaceholderPhoto(string photoId)
    {
        return new Photo
        {
            Id = photoId,
            Description = &quot;Photo temporarily unavailable&quot;,
            Width = 800,
            Height = 600,
            Color = &quot;#CCCCCC&quot;,
            User = new User { Name = &quot;Placeholder&quot;, Username = &quot;placeholder&quot; },
            Urls = new Urls
            {
                Regular = &quot;https://via.placeholder.com/800x600?text=Photo+Unavailable&quot;,
                Small = &quot;https://via.placeholder.com/400x300?text=Photo+Unavailable&quot;,
                Thumbnail = &quot;https://via.placeholder.com/200x150?text=Photo+Unavailable&quot;
            }
        };
    }
}
</code></pre>
<h2 id="monitoring-and-observability">Monitoring and Observability</h2>
<h3 id="1-structured-logging">1. Structured Logging</h3>
<pre><code class="lang-csharp">public class ObservablePhotoService
{
    private readonly UnsplasharpClient _client;
    private readonly ILogger&lt;ObservablePhotoService&gt; _logger;
    private readonly IMetrics _metrics;

    public ObservablePhotoService(
        UnsplasharpClient client,
        ILogger&lt;ObservablePhotoService&gt; logger,
        IMetrics metrics)
    {
        _client = client;
        _logger = logger;
        _metrics = metrics;
    }

    public async Task&lt;Photo?&gt; GetPhotoAsync(string photoId)
    {
        using var activity = Activity.StartActivity(&quot;GetPhoto&quot;);
        activity?.SetTag(&quot;photo.id&quot;, photoId);

        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation(&quot;Fetching photo {PhotoId}&quot;, photoId);

            var photo = await _client.GetPhotoAsync(photoId);

            stopwatch.Stop();

            _logger.LogInformation(&quot;Successfully fetched photo {PhotoId} in {Duration}ms&quot;,
                photoId, stopwatch.ElapsedMilliseconds);

            // Record metrics
            _metrics.Increment(&quot;unsplash.photo.requests&quot;, new[] { &quot;status:success&quot; });
            _metrics.Histogram(&quot;unsplash.photo.duration&quot;, stopwatch.ElapsedMilliseconds);

            activity?.SetTag(&quot;photo.width&quot;, photo.Width);
            activity?.SetTag(&quot;photo.height&quot;, photo.Height);
            activity?.SetTag(&quot;photo.likes&quot;, photo.Likes);

            return photo;
        }
        catch (UnsplasharpNotFoundException ex)
        {
            stopwatch.Stop();

            _logger.LogWarning(&quot;Photo {PhotoId} not found&quot;, photoId);

            _metrics.Increment(&quot;unsplash.photo.requests&quot;, new[] { &quot;status:not_found&quot; });

            activity?.SetStatus(ActivityStatusCode.Error, &quot;Photo not found&quot;);

            return null;
        }
        catch (UnsplasharpRateLimitException ex)
        {
            stopwatch.Stop();

            _logger.LogWarning(&quot;Rate limited fetching photo {PhotoId}. Remaining: {Remaining}/{Total}, Reset: {Reset}&quot;,
                photoId, ex.RateLimitRemaining, ex.RateLimit, ex.RateLimitReset);

            _metrics.Increment(&quot;unsplash.photo.requests&quot;, new[] { &quot;status:rate_limited&quot; });
            _metrics.Gauge(&quot;unsplash.rate_limit.remaining&quot;, ex.RateLimitRemaining ?? 0);

            activity?.SetStatus(ActivityStatusCode.Error, &quot;Rate limited&quot;);

            throw;
        }
        catch (UnsplasharpException ex)
        {
            stopwatch.Stop();

            _logger.LogError(ex, &quot;Error fetching photo {PhotoId}: {ErrorMessage}&quot;, photoId, ex.Message);

            _metrics.Increment(&quot;unsplash.photo.requests&quot;, new[] { &quot;status:error&quot; });

            activity?.SetStatus(ActivityStatusCode.Error, ex.Message);

            throw;
        }
    }
}
</code></pre>
<h3 id="2-custom-metrics">2. Custom Metrics</h3>
<pre><code class="lang-csharp">public class UnsplashMetricsService
{
    private readonly IMetrics _metrics;
    private readonly Timer _metricsTimer;

    public UnsplashMetricsService(IMetrics metrics, UnsplasharpClient client)
    {
        _metrics = metrics;

        // Periodically report rate limit status
        _metricsTimer = new Timer(async _ =&gt;
        {
            _metrics.Gauge(&quot;unsplash.rate_limit.remaining&quot;, client.RateLimitRemaining);
            _metrics.Gauge(&quot;unsplash.rate_limit.total&quot;, client.MaxRateLimit);

            var utilizationPercent = client.MaxRateLimit &gt; 0
                ? (double)(client.MaxRateLimit - client.RateLimitRemaining) / client.MaxRateLimit * 100
                : 0;

            _metrics.Gauge(&quot;unsplash.rate_limit.utilization_percent&quot;, utilizationPercent);

        }, null, TimeSpan.Zero, TimeSpan.FromMinutes(1));
    }

    public void RecordSearchMetrics(string query, int resultCount, TimeSpan duration)
    {
        _metrics.Increment(&quot;unsplash.search.requests&quot;);
        _metrics.Histogram(&quot;unsplash.search.duration&quot;, duration.TotalMilliseconds);
        _metrics.Histogram(&quot;unsplash.search.result_count&quot;, resultCount);

        // Tag by query type
        var queryType = DetermineQueryType(query);
        _metrics.Increment(&quot;unsplash.search.by_type&quot;, new[] { $&quot;type:{queryType}&quot; });
    }

    private string DetermineQueryType(string query)
    {
        if (query.Contains(&quot; &quot;)) return &quot;multi_word&quot;;
        if (query.Length &gt; 20) return &quot;long&quot;;
        if (query.All(char.IsLetter)) return &quot;text_only&quot;;
        return &quot;simple&quot;;
    }

    public void Dispose()
    {
        _metricsTimer?.Dispose();
    }
}
</code></pre>
<h3 id="3-application-insights-integration">3. Application Insights Integration</h3>
<pre><code class="lang-csharp">public class ApplicationInsightsPhotoService
{
    private readonly UnsplasharpClient _client;
    private readonly TelemetryClient _telemetryClient;

    public ApplicationInsightsPhotoService(UnsplasharpClient client, TelemetryClient telemetryClient)
    {
        _client = client;
        _telemetryClient = telemetryClient;
    }

    public async Task&lt;List&lt;Photo&gt;&gt; SearchPhotosWithTelemetry(string query, int perPage = 20)
    {
        var stopwatch = Stopwatch.StartNew();
        var properties = new Dictionary&lt;string, string&gt;
        {
            [&quot;query&quot;] = query,
            [&quot;per_page&quot;] = perPage.ToString()
        };

        try
        {
            var photos = await _client.SearchPhotosAsync(query, perPage: perPage);

            stopwatch.Stop();

            var metrics = new Dictionary&lt;string, double&gt;
            {
                [&quot;duration_ms&quot;] = stopwatch.ElapsedMilliseconds,
                [&quot;result_count&quot;] = photos.Count,
                [&quot;rate_limit_remaining&quot;] = _client.RateLimitRemaining,
                [&quot;total_results&quot;] = _client.LastPhotosSearchTotalResults
            };

            _telemetryClient.TrackEvent(&quot;UnsplashSearchSuccess&quot;, properties, metrics);

            return photos;
        }
        catch (UnsplasharpRateLimitException ex)
        {
            stopwatch.Stop();

            properties[&quot;error_type&quot;] = &quot;rate_limit&quot;;
            properties[&quot;rate_limit_reset&quot;] = ex.RateLimitReset?.ToString() ?? &quot;unknown&quot;;

            var metrics = new Dictionary&lt;string, double&gt;
            {
                [&quot;duration_ms&quot;] = stopwatch.ElapsedMilliseconds,
                [&quot;rate_limit_remaining&quot;] = ex.RateLimitRemaining ?? 0,
                [&quot;rate_limit_total&quot;] = ex.RateLimit ?? 0
            };

            _telemetryClient.TrackEvent(&quot;UnsplashSearchRateLimited&quot;, properties, metrics);

            throw;
        }
        catch (UnsplasharpException ex)
        {
            stopwatch.Stop();

            properties[&quot;error_type&quot;] = ex.GetType().Name;
            properties[&quot;error_message&quot;] = ex.Message;

            var metrics = new Dictionary&lt;string, double&gt;
            {
                [&quot;duration_ms&quot;] = stopwatch.ElapsedMilliseconds
            };

            _telemetryClient.TrackEvent(&quot;UnsplashSearchError&quot;, properties, metrics);
            _telemetryClient.TrackException(ex);

            throw;
        }
    }
}
</code></pre>
<hr>
<h2 id="summary">Summary</h2>
<p>This comprehensive testing and best practices guide provides:</p>
<h3 id="testing-strategy">Testing Strategy</h3>
<ul>
<li><strong>Unit Tests</strong>: Focus on business logic and error handling</li>
<li><strong>Integration Tests</strong>: Validate API contracts and error scenarios</li>
<li><strong>Performance Tests</strong>: Ensure acceptable response times and throughput</li>
</ul>
<h3 id="best-practices-1">Best Practices</h3>
<ul>
<li><strong>Error Handling</strong>: Use specific exception types and appropriate logging</li>
<li><strong>Caching</strong>: Implement intelligent caching with appropriate TTL</li>
<li><strong>Rate Limiting</strong>: Proactive monitoring and graceful handling</li>
<li><strong>Security</strong>: Secure API key management and input validation</li>
</ul>
<h3 id="production-readiness">Production Readiness</h3>
<ul>
<li><strong>Configuration</strong>: Environment-specific settings and validation</li>
<li><strong>Health Checks</strong>: Monitor API availability and rate limits</li>
<li><strong>Resilience</strong>: Circuit breakers and graceful degradation</li>
<li><strong>Observability</strong>: Structured logging, metrics, and distributed tracing</li>
</ul>
<h3 id="key-takeaways">Key Takeaways</h3>
<ol>
<li>Always test error scenarios, not just happy paths</li>
<li>Implement comprehensive monitoring and alerting</li>
<li>Use dependency injection for better testability</li>
<li>Handle rate limits proactively, not reactively</li>
<li>Cache intelligently based on content characteristics</li>
<li>Validate inputs and secure API keys properly</li>
<li>Plan for graceful degradation when the API is unavailable</li>
</ol>
<p>Following these practices will help you build robust, maintainable, and production-ready applications with Unsplasharp.</p>
<pre><code></code></pre>
<pre><code></code></pre>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/rootasjey/unsplasharp/blob/master/docs/testing-best-practices.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
