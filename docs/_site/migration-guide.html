<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Migration and Upgrade Guide | Unsplasharp Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Migration and Upgrade Guide | Unsplasharp Documentation ">
      
      
      <link rel="icon" href="favicon.ico">
      <link rel="stylesheet" href="public/docfx.min.css">
      <link rel="stylesheet" href="public/main.css">
      <meta name="docfx:navrel" content="toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="">
      
      
      <meta name="docfx:docurl" content="https://github.com/rootasjey/unsplasharp/blob/master/docs/migration-guide.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="index.html">
            <img id="logo" class="svg" src="logo.svg" alt="Unsplasharp">
            Unsplasharp
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">

      <div class="content">
        <div class="actionbar">

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="migration-and-upgrade-guide">Migration and Upgrade Guide</h1>

<p>This guide helps you migrate from older versions of Unsplasharp and adopt new features like comprehensive error handling, IHttpClientFactory integration, and modern async patterns.</p>
<h2 id="table-of-contents">Table of Contents</h2>
<ul>
<li><a href="#version-compatibility">Version Compatibility</a></li>
<li><a href="#breaking-changes">Breaking Changes</a></li>
<li><a href="#new-features-overview">New Features Overview</a></li>
<li><a href="#migration-strategies">Migration Strategies</a></li>
<li><a href="#error-handling-migration">Error Handling Migration</a></li>
<li><a href="#ihttpclientfactory-migration">IHttpClientFactory Migration</a></li>
<li><a href="#logging-integration">Logging Integration</a></li>
<li><a href="#performance-improvements">Performance Improvements</a></li>
<li><a href="#best-practices-updates">Best Practices Updates</a></li>
</ul>
<h2 id="version-compatibility">Version Compatibility</h2>
<h3 id="supported-net-versions">Supported .NET Versions</h3>
<table>
<thead>
<tr>
<th>Unsplasharp Version</th>
<th>.NET Framework</th>
<th>.NET Core</th>
<th>.NET 5+</th>
</tr>
</thead>
<tbody>
<tr>
<td>1.x</td>
<td>4.6.1+</td>
<td>2.0+</td>
<td>❌</td>
</tr>
<tr>
<td>2.x</td>
<td>4.6.1+</td>
<td>2.0+</td>
<td>5.0+</td>
</tr>
<tr>
<td>3.x (Current)</td>
<td>4.6.1+</td>
<td>2.0+</td>
<td>5.0+</td>
</tr>
</tbody>
</table>
<h3 id="api-compatibility">API Compatibility</h3>
<ul>
<li><strong>Backward Compatible</strong>: All existing methods continue to work</li>
<li><strong>New Methods</strong>: Exception-throwing variants added (e.g., <code>GetPhotoAsync</code>)</li>
<li><strong>Enhanced Features</strong>: Logging, metrics, and error context added</li>
<li><strong>No Breaking Changes</strong>: Existing code will compile and run without modifications</li>
</ul>
<h2 id="breaking-changes">Breaking Changes</h2>
<h3 id="none-">None! 🎉</h3>
<p>Unsplasharp maintains full backward compatibility. Your existing code will continue to work exactly as before.</p>
<pre><code class="lang-csharp">// This code from v1.x still works in v3.x
var client = new UnsplasharpClient(&quot;YOUR_APP_ID&quot;);
var photo = await client.GetRandomPhoto();
if (photo != null)
{
    Console.WriteLine(photo.Urls.Regular);
}
</code></pre>
<h2 id="new-features-overview">New Features Overview</h2>
<h3 id="1-comprehensive-error-handling">1. Comprehensive Error Handling</h3>
<ul>
<li><strong>New Exception Types</strong>: Specific exceptions for different error scenarios</li>
<li><strong>Rich Error Context</strong>: Detailed information about requests and responses</li>
<li><strong>Correlation IDs</strong>: For better debugging and monitoring</li>
<li><strong>Rate Limit Awareness</strong>: Automatic rate limit detection and handling</li>
</ul>
<h3 id="2-ihttpclientfactory-integration">2. IHttpClientFactory Integration</h3>
<ul>
<li><strong>Modern HTTP Management</strong>: Proper HttpClient lifecycle management</li>
<li><strong>Dependency Injection</strong>: Seamless integration with DI containers</li>
<li><strong>Connection Pooling</strong>: Better performance and resource utilization</li>
<li><strong>Configuration Centralization</strong>: Configure HTTP clients in one place</li>
</ul>
<h3 id="3-structured-logging">3. Structured Logging</h3>
<ul>
<li><strong>Microsoft.Extensions.Logging</strong>: Integration with standard logging framework</li>
<li><strong>Detailed Insights</strong>: HTTP requests, retries, rate limits, and errors</li>
<li><strong>Correlation Tracking</strong>: Link related log entries with correlation IDs</li>
<li><strong>Performance Metrics</strong>: Request timing and success rates</li>
</ul>
<h3 id="4-enhanced-async-support">4. Enhanced Async Support</h3>
<ul>
<li><strong>CancellationToken Support</strong>: All methods now support cancellation</li>
<li><strong>Better Exception Handling</strong>: Exception-throwing variants for better error handling</li>
<li><strong>Timeout Management</strong>: Configurable timeouts with proper cancellation</li>
</ul>
<h2 id="migration-strategies">Migration Strategies</h2>
<h3 id="strategy-1-gradual-migration-recommended">Strategy 1: Gradual Migration (Recommended)</h3>
<p>Migrate your codebase gradually by replacing method calls one at a time:</p>
<pre><code class="lang-csharp">// Step 1: Keep existing code working
var client = new UnsplasharpClient(&quot;YOUR_APP_ID&quot;);
var photo = await client.GetRandomPhoto(); // Old method

// Step 2: Add logging (optional)
var logger = loggerFactory.CreateLogger&lt;UnsplasharpClient&gt;();
var clientWithLogging = new UnsplasharpClient(&quot;YOUR_APP_ID&quot;, logger: logger);

// Step 3: Replace with exception-throwing methods
try
{
    var photo = await client.GetRandomPhotoAsync(); // New method
    // Handle success
}
catch (UnsplasharpException ex)
{
    // Handle errors
}

// Step 4: Add IHttpClientFactory (for new projects or major refactoring)
services.AddUnsplasharp(&quot;YOUR_APP_ID&quot;);
</code></pre>
<h3 id="strategy-2-new-project-setup">Strategy 2: New Project Setup</h3>
<p>For new projects, start with the modern approach:</p>
<pre><code class="lang-csharp">// Program.cs
services.AddUnsplasharp(options =&gt;
{
    options.ApplicationId = configuration[&quot;Unsplash:ApplicationId&quot;];
    options.ConfigureHttpClient = client =&gt;
    {
        client.Timeout = TimeSpan.FromSeconds(30);
    };
});

// Service class
public class PhotoService
{
    private readonly UnsplasharpClient _client;
    private readonly ILogger&lt;PhotoService&gt; _logger;

    public PhotoService(UnsplasharpClient client, ILogger&lt;PhotoService&gt; logger)
    {
        _client = client;
        _logger = logger;
    }

    public async Task&lt;Photo?&gt; GetPhotoAsync(string photoId)
    {
        try
        {
            return await _client.GetPhotoAsync(photoId);
        }
        catch (UnsplasharpNotFoundException)
        {
            _logger.LogWarning(&quot;Photo {PhotoId} not found&quot;, photoId);
            return null;
        }
        catch (UnsplasharpException ex)
        {
            _logger.LogError(ex, &quot;Error getting photo {PhotoId}&quot;, photoId);
            throw;
        }
    }
}
</code></pre>
<h2 id="error-handling-migration">Error Handling Migration</h2>
<h3 id="before-basic-error-handling">Before: Basic Error Handling</h3>
<pre><code class="lang-csharp">// Old approach - null checking
var photo = await client.GetRandomPhoto();
if (photo == null)
{
    Console.WriteLine(&quot;Failed to get photo&quot;);
    return;
}

// Process photo
Console.WriteLine($&quot;Photo by {photo.User.Name}&quot;);
</code></pre>
<h3 id="after-comprehensive-error-handling">After: Comprehensive Error Handling</h3>
<pre><code class="lang-csharp">// New approach - exception handling
try
{
    var photo = await client.GetRandomPhotoAsync();
    Console.WriteLine($&quot;Photo by {photo.User.Name}&quot;);
}
catch (UnsplasharpNotFoundException)
{
    Console.WriteLine(&quot;Photo not found&quot;);
}
catch (UnsplasharpRateLimitException ex)
{
    Console.WriteLine($&quot;Rate limited. Reset at: {ex.RateLimitReset}&quot;);
    // Implement backoff strategy
}
catch (UnsplasharpAuthenticationException)
{
    Console.WriteLine(&quot;Invalid API key&quot;);
}
catch (UnsplasharpNetworkException ex) when (ex.IsRetryable)
{
    Console.WriteLine(&quot;Network error - retrying...&quot;);
    // Implement retry logic
}
catch (UnsplasharpException ex)
{
    Console.WriteLine($&quot;API error: {ex.Message}&quot;);
    
    // Access rich error context
    if (ex.Context != null)
    {
        Console.WriteLine($&quot;Correlation ID: {ex.Context.CorrelationId}&quot;);
        Console.WriteLine($&quot;Request URL: {ex.RequestUrl}&quot;);
    }
}
</code></pre>
<h3 id="migration-helper">Migration Helper</h3>
<pre><code class="lang-csharp">public static class MigrationHelper
{
    /// &lt;summary&gt;
    /// Wraps old-style methods to provide exception-based error handling
    /// &lt;/summary&gt;
    public static async Task&lt;T&gt; WrapWithExceptions&lt;T&gt;(Func&lt;Task&lt;T?&gt;&gt; oldMethod) where T : class
    {
        var result = await oldMethod();
        if (result == null)
        {
            throw new InvalidOperationException(&quot;Operation returned null - this may indicate an API error&quot;);
        }
        return result;
    }
}

// Usage
try
{
    var photo = await MigrationHelper.WrapWithExceptions(() =&gt; client.GetRandomPhoto());
    // Process photo
}
catch (Exception ex)
{
    // Handle error
}
</code></pre>
<h2 id="ihttpclientfactory-migration">IHttpClientFactory Migration</h2>
<h3 id="before-manual-httpclient-management">Before: Manual HttpClient Management</h3>
<pre><code class="lang-csharp">// Old approach - basic client creation
var client = new UnsplasharpClient(&quot;YOUR_APP_ID&quot;);

// Or with logging
var logger = loggerFactory.CreateLogger&lt;UnsplasharpClient&gt;();
var client = new UnsplasharpClient(&quot;YOUR_APP_ID&quot;, logger: logger);
</code></pre>
<h3 id="after-ihttpclientfactory-integration">After: IHttpClientFactory Integration</h3>
<pre><code class="lang-csharp">// New approach - dependency injection setup
// In Program.cs or Startup.cs
services.AddUnsplasharp(&quot;YOUR_APP_ID&quot;);

// Or with configuration
services.AddUnsplasharp(options =&gt;
{
    options.ApplicationId = configuration[&quot;Unsplash:ApplicationId&quot;];
    options.Secret = configuration[&quot;Unsplash:Secret&quot;];
    options.ConfigureHttpClient = client =&gt;
    {
        client.Timeout = TimeSpan.FromSeconds(60);
        client.DefaultRequestHeaders.UserAgent.ParseAdd(&quot;MyApp/1.0&quot;);
    };
});

// In your service
public class PhotoService
{
    private readonly UnsplasharpClient _client;

    public PhotoService(UnsplasharpClient client)
    {
        _client = client; // Injected with proper HttpClient management
    }
}
</code></pre>
<h3 id="manual-ihttpclientfactory-setup">Manual IHttpClientFactory Setup</h3>
<p>If you can't use the extension method:</p>
<pre><code class="lang-csharp">// Manual setup
services.AddHttpClient();
services.AddScoped&lt;UnsplasharpClient&gt;(provider =&gt;
{
    var httpClientFactory = provider.GetRequiredService&lt;IHttpClientFactory&gt;();
    var logger = provider.GetService&lt;ILogger&lt;UnsplasharpClient&gt;&gt;();
    
    return new UnsplasharpClient(
        applicationId: &quot;YOUR_APP_ID&quot;,
        logger: logger,
        httpClientFactory: httpClientFactory
    );
});
</code></pre>
<h2 id="logging-integration">Logging Integration</h2>
<h3 id="before-no-logging">Before: No Logging</h3>
<pre><code class="lang-csharp">var client = new UnsplasharpClient(&quot;YOUR_APP_ID&quot;);
var photo = await client.GetRandomPhoto();
// No visibility into what's happening
</code></pre>
<h3 id="after-structured-logging">After: Structured Logging</h3>
<pre><code class="lang-csharp">// Setup logging
services.AddLogging(builder =&gt;
{
    builder.AddConsole();
    builder.AddDebug();
    builder.SetMinimumLevel(LogLevel.Information);
});

// Client with logging
var logger = serviceProvider.GetRequiredService&lt;ILogger&lt;UnsplasharpClient&gt;&gt;();
var client = new UnsplasharpClient(&quot;YOUR_APP_ID&quot;, logger: logger);

// Or with DI
services.AddUnsplasharp(&quot;YOUR_APP_ID&quot;); // Automatically includes logging

// Now you get detailed logs:
// [Information] Making HTTP request to https://api.unsplash.com/photos/random
// [Debug] Rate limit: 4999/5000
// [Information] Request completed in 245ms
</code></pre>
<h3 id="custom-logging-configuration">Custom Logging Configuration</h3>
<pre><code class="lang-csharp">services.AddLogging(builder =&gt;
{
    builder.AddConsole(options =&gt;
    {
        options.IncludeScopes = true;
        options.TimestampFormat = &quot;yyyy-MM-dd HH:mm:ss &quot;;
    });
    
    // Set specific log levels
    builder.AddFilter(&quot;Unsplasharp&quot;, LogLevel.Information);
    builder.AddFilter(&quot;System.Net.Http&quot;, LogLevel.Warning);
});
</code></pre>
<h2 id="performance-improvements">Performance Improvements</h2>
<h3 id="connection-pooling">Connection Pooling</h3>
<p>The new version automatically uses connection pooling when IHttpClientFactory is available:</p>
<pre><code class="lang-csharp">// Old: Each client instance creates its own HttpClient
var client1 = new UnsplasharpClient(&quot;APP_ID&quot;);
var client2 = new UnsplasharpClient(&quot;APP_ID&quot;); // Creates another HttpClient

// New: Shared connection pool
services.AddUnsplasharp(&quot;APP_ID&quot;);
// All injected clients share the same optimized HttpClient pool
</code></pre>
<h3 id="retry-policies">Retry Policies</h3>
<p>Built-in retry policies with exponential backoff:</p>
<pre><code class="lang-csharp">// Automatic retry for transient failures
try
{
    var photo = await client.GetPhotoAsync(&quot;photo-id&quot;);
}
catch (UnsplasharpNetworkException ex) when (ex.IsRetryable)
{
    // The client already attempted retries with exponential backoff
    // This exception means all retries were exhausted
}
</code></pre>
<h3 id="rate-limit-optimization">Rate Limit Optimization</h3>
<p>Better rate limit handling:</p>
<pre><code class="lang-csharp">// Automatic rate limit tracking
Console.WriteLine($&quot;Rate limit: {client.RateLimitRemaining}/{client.MaxRateLimit}&quot;);

// Smart retry on rate limit exceeded
try
{
    var photos = await client.SearchPhotosAsync(&quot;nature&quot;);
}
catch (UnsplasharpRateLimitException ex)
{
    // Exception includes exact reset time
    var waitTime = ex.RateLimitReset - DateTimeOffset.UtcNow;
    await Task.Delay(waitTime);
    // Retry the request
}
</code></pre>
<h2 id="best-practices-updates">Best Practices Updates</h2>
<h3 id="1-use-cancellationtokens">1. Use CancellationTokens</h3>
<p>All methods now support cancellation tokens:</p>
<pre><code class="lang-csharp">// Before: No cancellation support
var photos = await client.SearchPhotos(&quot;nature&quot;);

// After: With cancellation support
using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
try
{
    var photos = await client.SearchPhotosAsync(&quot;nature&quot;, cancellationToken: cts.Token);
}
catch (OperationCanceledException)
{
    Console.WriteLine(&quot;Search timed out&quot;);
}
</code></pre>
<h3 id="2-implement-proper-error-handling">2. Implement Proper Error Handling</h3>
<pre><code class="lang-csharp">// Before: Basic null checking
public async Task&lt;List&lt;Photo&gt;&gt; GetPhotosOldWay(string query)
{
    var photos = await client.SearchPhotos(query);
    return photos ?? new List&lt;Photo&gt;();
}

// After: Comprehensive error handling
public async Task&lt;List&lt;Photo&gt;&gt; GetPhotosNewWay(string query)
{
    try
    {
        return await client.SearchPhotosAsync(query);
    }
    catch (UnsplasharpNotFoundException)
    {
        return new List&lt;Photo&gt;();
    }
    catch (UnsplasharpRateLimitException ex)
    {
        logger.LogWarning(&quot;Rate limited, waiting {Delay}ms&quot;, ex.TimeUntilReset?.TotalMilliseconds);
        throw; // Let caller handle rate limiting
    }
    catch (UnsplasharpException ex)
    {
        logger.LogError(ex, &quot;Search failed for query: {Query}&quot;, query);
        throw;
    }
}
</code></pre>
<h3 id="3-use-dependency-injection">3. Use Dependency Injection</h3>
<pre><code class="lang-csharp">// Before: Manual instantiation
public class PhotoService
{
    private readonly UnsplasharpClient _client;

    public PhotoService()
    {
        _client = new UnsplasharpClient(&quot;YOUR_APP_ID&quot;);
    }
}

// After: Dependency injection
public class PhotoService
{
    private readonly UnsplasharpClient _client;
    private readonly ILogger&lt;PhotoService&gt; _logger;

    public PhotoService(UnsplasharpClient client, ILogger&lt;PhotoService&gt; logger)
    {
        _client = client;
        _logger = logger;
    }
}

// Registration
services.AddUnsplasharp(&quot;YOUR_APP_ID&quot;);
services.AddScoped&lt;PhotoService&gt;();
</code></pre>
<h3 id="4-implement-caching">4. Implement Caching</h3>
<pre><code class="lang-csharp">// Enhanced caching with error handling
public class CachedPhotoService
{
    private readonly UnsplasharpClient _client;
    private readonly IMemoryCache _cache;
    private readonly ILogger&lt;CachedPhotoService&gt; _logger;

    public async Task&lt;Photo?&gt; GetPhotoAsync(string photoId)
    {
        var cacheKey = $&quot;photo:{photoId}&quot;;

        if (_cache.TryGetValue(cacheKey, out Photo cachedPhoto))
        {
            return cachedPhoto;
        }

        try
        {
            var photo = await _client.GetPhotoAsync(photoId);
            _cache.Set(cacheKey, photo, TimeSpan.FromHours(1));
            return photo;
        }
        catch (UnsplasharpNotFoundException)
        {
            // Cache negative results for shorter time
            _cache.Set(cacheKey, (Photo?)null, TimeSpan.FromMinutes(5));
            return null;
        }
        catch (UnsplasharpRateLimitException)
        {
            // Don't cache rate limit errors
            throw;
        }
        catch (UnsplasharpException ex)
        {
            _logger.LogError(ex, &quot;Failed to get photo {PhotoId}&quot;, photoId);
            throw;
        }
    }
}
</code></pre>
<h2 id="troubleshooting-common-migration-issues">Troubleshooting Common Migration Issues</h2>
<h3 id="issue-1-null-reference-exceptions">Issue 1: Null Reference Exceptions</h3>
<p><strong>Problem</strong>: Code that worked before now throws null reference exceptions.</p>
<p><strong>Cause</strong>: You might be using new exception-throwing methods without proper error handling.</p>
<p><strong>Solution</strong>:</p>
<pre><code class="lang-csharp">// If you get NullReferenceException here:
var photo = await client.GetPhotoAsync(&quot;invalid-id&quot;);
Console.WriteLine(photo.Description); // NullReferenceException

// Change to:
try
{
    var photo = await client.GetPhotoAsync(&quot;invalid-id&quot;);
    Console.WriteLine(photo.Description);
}
catch (UnsplasharpNotFoundException)
{
    Console.WriteLine(&quot;Photo not found&quot;);
}
</code></pre>
<h3 id="issue-2-httpclient-disposal-issues">Issue 2: HttpClient Disposal Issues</h3>
<p><strong>Problem</strong>: &quot;Cannot access a disposed object&quot; errors.</p>
<p><strong>Cause</strong>: Manual HttpClient management conflicts with IHttpClientFactory.</p>
<p><strong>Solution</strong>:</p>
<pre><code class="lang-csharp">// Don't do this:
using var httpClient = new HttpClient();
var client = new UnsplasharpClient(&quot;APP_ID&quot;, httpClientFactory: someFactory);

// Do this instead:
services.AddUnsplasharp(&quot;APP_ID&quot;); // Let DI handle lifecycle
</code></pre>
<h3 id="issue-3-rate-limit-handling">Issue 3: Rate Limit Handling</h3>
<p><strong>Problem</strong>: Application stops working when rate limits are hit.</p>
<p><strong>Solution</strong>:</p>
<pre><code class="lang-csharp">public async Task&lt;T&gt; ExecuteWithRetry&lt;T&gt;(Func&lt;Task&lt;T&gt;&gt; operation, int maxRetries = 3)
{
    for (int attempt = 1; attempt &lt;= maxRetries; attempt++)
    {
        try
        {
            return await operation();
        }
        catch (UnsplasharpRateLimitException ex) when (attempt &lt; maxRetries)
        {
            var delay = ex.TimeUntilReset ?? TimeSpan.FromMinutes(1);
            await Task.Delay(delay);
        }
        catch (UnsplasharpNetworkException ex) when (ex.IsRetryable &amp;&amp; attempt &lt; maxRetries)
        {
            var delay = TimeSpan.FromSeconds(Math.Pow(2, attempt));
            await Task.Delay(delay);
        }
    }

    throw new InvalidOperationException($&quot;Operation failed after {maxRetries} attempts&quot;);
}

// Usage
var photo = await ExecuteWithRetry(() =&gt; client.GetPhotoAsync(&quot;photo-id&quot;));
</code></pre>
<h3 id="issue-4-configuration-problems">Issue 4: Configuration Problems</h3>
<p><strong>Problem</strong>: API key not being recognized or configuration not loading.</p>
<p><strong>Solution</strong>:</p>
<pre><code class="lang-csharp">// Check configuration loading
var config = builder.Configuration.GetSection(&quot;Unsplash&quot;).Get&lt;UnsplashConfiguration&gt;();
if (string.IsNullOrEmpty(config?.ApplicationId))
{
    throw new InvalidOperationException(&quot;Unsplash ApplicationId not configured&quot;);
}

// Validate at startup
services.AddUnsplasharp(options =&gt;
{
    options.ApplicationId = config.ApplicationId ??
        throw new ArgumentNullException(nameof(config.ApplicationId));
});
</code></pre>
<h2 id="migration-checklist">Migration Checklist</h2>
<h3 id="phase-1-preparation">Phase 1: Preparation</h3>
<ul>
<li>[ ] Review current Unsplasharp usage in your codebase</li>
<li>[ ] Identify error handling patterns</li>
<li>[ ] Plan migration strategy (gradual vs. complete)</li>
<li>[ ] Set up logging infrastructure</li>
<li>[ ] Update NuGet package</li>
</ul>
<h3 id="phase-2-basic-migration">Phase 2: Basic Migration</h3>
<ul>
<li>[ ] Add logging to existing clients</li>
<li>[ ] Replace null checks with try-catch blocks</li>
<li>[ ] Add CancellationToken support to async methods</li>
<li>[ ] Test existing functionality</li>
</ul>
<h3 id="phase-3-advanced-features">Phase 3: Advanced Features</h3>
<ul>
<li>[ ] Implement IHttpClientFactory integration</li>
<li>[ ] Add comprehensive error handling</li>
<li>[ ] Implement retry policies</li>
<li>[ ] Add performance monitoring</li>
<li>[ ] Update caching strategies</li>
</ul>
<h3 id="phase-4-optimization">Phase 4: Optimization</h3>
<ul>
<li>[ ] Review and optimize HTTP client configuration</li>
<li>[ ] Implement connection pooling</li>
<li>[ ] Add metrics collection</li>
<li>[ ] Performance testing</li>
<li>[ ] Documentation updates</li>
</ul>
<h2 id="testing-your-migration">Testing Your Migration</h2>
<h3 id="unit-testing">Unit Testing</h3>
<pre><code class="lang-csharp">[Test]
public async Task GetPhoto_WithValidId_ReturnsPhoto()
{
    // Arrange
    var mockFactory = new Mock&lt;IHttpClientFactory&gt;();
    var mockLogger = new Mock&lt;ILogger&lt;UnsplasharpClient&gt;&gt;();
    var client = new UnsplasharpClient(&quot;test-app-id&quot;, logger: mockLogger.Object, httpClientFactory: mockFactory.Object);

    // Act &amp; Assert
    try
    {
        var photo = await client.GetPhotoAsync(&quot;valid-photo-id&quot;);
        Assert.IsNotNull(photo);
    }
    catch (UnsplasharpNotFoundException)
    {
        // Expected for invalid test ID
        Assert.Pass(&quot;Exception handling working correctly&quot;);
    }
}

[Test]
public async Task GetPhoto_WithInvalidId_ThrowsNotFoundException()
{
    var client = new UnsplasharpClient(&quot;test-app-id&quot;);

    await Assert.ThrowsAsync&lt;UnsplasharpNotFoundException&gt;(
        () =&gt; client.GetPhotoAsync(&quot;invalid-photo-id&quot;));
}
</code></pre>
<h3 id="integration-testing">Integration Testing</h3>
<pre><code class="lang-csharp">[Test]
public async Task Integration_SearchPhotos_ReturnsResults()
{
    var client = new UnsplasharpClient(TestConfiguration.ApplicationId);

    var photos = await client.SearchPhotosAsync(&quot;nature&quot;, perPage: 5);

    Assert.IsNotEmpty(photos);
    Assert.All(photos, photo =&gt; Assert.IsNotNull(photo.Id));
}
</code></pre>
<h2 id="performance-comparison">Performance Comparison</h2>
<h3 id="before-migration">Before Migration</h3>
<ul>
<li>Manual HttpClient management</li>
<li>No connection pooling</li>
<li>Basic error handling</li>
<li>No retry logic</li>
<li>No structured logging</li>
</ul>
<h3 id="after-migration">After Migration</h3>
<ul>
<li>Optimized HttpClient with connection pooling</li>
<li>Automatic retry with exponential backoff</li>
<li>Comprehensive error handling with context</li>
<li>Structured logging with correlation IDs</li>
<li>Rate limit awareness</li>
</ul>
<h3 id="expected-improvements">Expected Improvements</h3>
<ul>
<li><strong>Reduced memory usage</strong>: Better HttpClient lifecycle management</li>
<li><strong>Improved reliability</strong>: Automatic retries and better error handling</li>
<li><strong>Better observability</strong>: Structured logging and metrics</li>
<li><strong>Enhanced performance</strong>: Connection pooling and optimized HTTP settings</li>
</ul>
<hr>
<h2 id="summary">Summary</h2>
<p>The migration to the latest Unsplasharp version provides significant improvements while maintaining full backward compatibility. Key benefits include:</p>
<p>✅ <strong>Zero Breaking Changes</strong> - Existing code continues to work
✅ <strong>Enhanced Error Handling</strong> - Specific exceptions with rich context
✅ <strong>Modern HTTP Management</strong> - IHttpClientFactory integration
✅ <strong>Structured Logging</strong> - Better observability and debugging
✅ <strong>Improved Performance</strong> - Connection pooling and retry policies
✅ <strong>Better Testing</strong> - Easier to mock and test</p>
<p>Take your time with the migration and adopt new features gradually. The investment in proper error handling and logging will pay dividends in production reliability and maintainability.</p>
<pre><code></code></pre>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/rootasjey/unsplasharp/blob/master/docs/migration-guide.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
