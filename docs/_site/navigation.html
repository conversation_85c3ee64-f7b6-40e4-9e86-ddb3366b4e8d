<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Documentation Navigation Guide | Unsplasharp Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Documentation Navigation Guide | Unsplasharp Documentation ">
      
      
      <link rel="icon" href="favicon.ico">
      <link rel="stylesheet" href="public/docfx.min.css">
      <link rel="stylesheet" href="public/main.css">
      <meta name="docfx:navrel" content="toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="">
      
      
      <meta name="docfx:docurl" content="https://github.com/rootasjey/unsplasharp/blob/master/docs/navigation.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="index.html">
            <img id="logo" class="svg" src="logo.svg" alt="Unsplasharp">
            Unsplasharp
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">

      <div class="content">
        <div class="actionbar">

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="documentation-navigation-guide">Documentation Navigation Guide</h1>

<p>This guide helps you navigate the Unsplasharp documentation efficiently based on your needs and experience level.</p>
<h2 id="-find-what-you-need">🎯 Find What You Need</h2>
<h3 id="im-new-to-unsplasharp">I'm New to Unsplasharp</h3>
<p><strong>Start Here:</strong></p>
<ol>
<li><a href="docs/getting-started.html">Getting Started Guide</a> - Complete setup and first examples</li>
<li><a href="docs/obtaining-an-api-key.html">API Key Setup</a> - Get your Unsplash credentials</li>
<li><a href="code-examples.html#basic-operations">Basic Examples</a> - Simple code to get you started</li>
<li><a href="error-handling.html#basic-error-handling">Error Handling Basics</a> - Handle common errors</li>
</ol>
<p><strong>Next Steps:</strong></p>
<ul>
<li><a href="api-reference.html">API Reference</a> - Learn about available methods</li>
<li><a href="models-reference.html">Model Reference</a> - Understand data structures</li>
</ul>
<h3 id="i-want-to-build-something-specific">I Want to Build Something Specific</h3>
<h4 id="photo-search-application">Photo Search Application</h4>
<ol>
<li><a href="code-examples.html#search-and-discovery">Search Examples</a> - Search implementation</li>
<li><a href="advanced-usage.html#filtering-and-search-optimization">Advanced Search</a> - Filters and optimization</li>
<li><a href="advanced-usage.html#advanced-pagination-strategies">Pagination</a> - Handle large result sets</li>
<li><a href="advanced-usage.html#performance-optimization">Caching</a> - Improve performance</li>
</ol>
<h4 id="photo-galleryviewer">Photo Gallery/Viewer</h4>
<ol>
<li><a href="api-reference.html#photo-methods">Photo Retrieval</a> - Get photos by ID</li>
<li><a href="api-reference.html#user-methods">User Profiles</a> - Display photographer info</li>
<li><a href="api-reference.html#collection-methods">Collections</a> - Browse curated collections</li>
<li><a href="code-examples.html#image-processing-and-download">Download Images</a> - Save photos locally</li>
</ol>
<h4 id="web-application-integration">Web Application Integration</h4>
<ol>
<li><a href="code-examples.html#web-application-integration">ASP.NET Core Examples</a> - Web API integration</li>
<li><a href="http-client-factory.html">Dependency Injection</a> - Proper DI setup</li>
<li><a href="advanced-usage.html#performance-optimization">Caching Strategies</a> - Web app caching</li>
<li><a href="error-handling.html">Error Handling</a> - Web-specific error handling</li>
</ol>
<h4 id="desktop-application">Desktop Application</h4>
<ol>
<li><a href="code-examples.html#desktop-application-examples">WPF Examples</a> - Desktop UI integration</li>
<li><a href="code-examples.html#desktop-application-examples">Console Examples</a> - Command-line tools</li>
<li><a href="code-examples.html#background-services">Background Services</a> - Long-running tasks</li>
<li><a href="code-examples.html#image-processing-and-download">Local Storage</a> - File management</li>
</ol>
<h4 id="backgroundservice-applications">Background/Service Applications</h4>
<ol>
<li><a href="code-examples.html#background-services">Background Services</a> - Service implementation</li>
<li><a href="advanced-usage.html#batch-operations">Batch Processing</a> - Handle multiple operations</li>
<li><a href="advanced-usage.html#performance-optimization">Rate Limiting</a> - Avoid API limits</li>
<li><a href="testing-best-practices.html#monitoring-and-observability">Monitoring</a> - Track performance</li>
</ol>
<h3 id="im-having-problems">I'm Having Problems</h3>
<h4 id="common-issues">Common Issues</h4>
<table>
<thead>
<tr>
<th>Problem</th>
<th>Solution</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>API key not working</strong></td>
<td><a href="docs/obtaining-an-api-key.html">API Key Setup</a></td>
</tr>
<tr>
<td><strong>Rate limit errors</strong></td>
<td><a href="error-handling.html#rate-limit-handling">Rate Limiting Guide</a></td>
</tr>
<tr>
<td><strong>Photos not loading</strong></td>
<td><a href="error-handling.html">Error Handling</a></td>
</tr>
<tr>
<td><strong>Slow performance</strong></td>
<td><a href="advanced-usage.html#performance-optimization">Performance Optimization</a></td>
</tr>
<tr>
<td><strong>Memory issues</strong></td>
<td><a href="testing-best-practices.html#best-practices">Best Practices</a></td>
</tr>
<tr>
<td><strong>Testing problems</strong></td>
<td><a href="testing-best-practices.html">Testing Guide</a></td>
</tr>
</tbody>
</table>
<h4 id="error-types">Error Types</h4>
<ul>
<li><strong>UnsplasharpNotFoundException</strong>: <a href="error-handling.html#handling-not-found-errors">Not Found Handling</a></li>
<li><strong>UnsplasharpRateLimitException</strong>: <a href="error-handling.html#rate-limit-handling">Rate Limit Handling</a></li>
<li><strong>UnsplasharpAuthenticationException</strong>: <a href="error-handling.html#authentication-errors">Auth Issues</a></li>
<li><strong>UnsplasharpNetworkException</strong>: <a href="error-handling.html#network-errors">Network Problems</a></li>
</ul>
<h3 id="i-want-to-upgrademigrate">I Want to Upgrade/Migrate</h3>
<h4 id="from-older-versions">From Older Versions</h4>
<ol>
<li><a href="migration-guide.html">Migration Guide</a> - Complete upgrade guide</li>
<li><a href="migration-guide.html#breaking-changes">Breaking Changes</a> - What changed</li>
<li><a href="migration-guide.html#new-features-overview">New Features</a> - What's new</li>
<li><a href="migration-guide.html#migration-strategies">Migration Strategies</a> - How to upgrade</li>
</ol>
<h4 id="adopting-new-features">Adopting New Features</h4>
<ul>
<li><a href="migration-guide.html#error-handling-migration">Exception Handling</a> - New error handling</li>
<li><a href="migration-guide.html#ihttpclientfactory-migration">IHttpClientFactory</a> - Modern HTTP clients</li>
<li><a href="migration-guide.html#logging-integration">Logging</a> - Structured logging</li>
<li><a href="migration-guide.html#performance-improvements">Performance</a> - Speed improvements</li>
</ul>
<h3 id="im-building-for-production">I'm Building for Production</h3>
<h4 id="essential-reading">Essential Reading</h4>
<ol>
<li><a href="testing-best-practices.html">Testing Guide</a> - Comprehensive testing</li>
<li><a href="testing-best-practices.html#best-practices">Best Practices</a> - Production guidelines</li>
<li><a href="testing-best-practices.html#security-considerations">Security</a> - Secure implementation</li>
<li><a href="testing-best-practices.html#monitoring-and-observability">Monitoring</a> - Track your app</li>
</ol>
<h4 id="production-checklist">Production Checklist</h4>
<ul>
<li>[ ] <a href="error-handling.html">Error Handling</a> implemented</li>
<li>[ ] <a href="advanced-usage.html#performance-optimization">Rate Limiting</a> handled</li>
<li>[ ] <a href="advanced-usage.html#performance-optimization">Caching</a> configured</li>
<li>[ ] <a href="logging.html">Logging</a> set up</li>
<li>[ ] <a href="testing-best-practices.html#production-deployment">Health Checks</a> added</li>
<li>[ ] <a href="testing-best-practices.html">Tests</a> written</li>
<li>[ ] <a href="testing-best-practices.html#monitoring-and-observability">Monitoring</a> configured</li>
</ul>
<h2 id="-documentation-structure">📖 Documentation Structure</h2>
<h3 id="core-documentation-files">Core Documentation Files</h3>
<pre><code>docs/
├── index.md                    # Main documentation hub
├── api-reference.md           # Complete API documentation
├── models-reference.md        # Model classes documentation
├── advanced-usage.md          # Advanced patterns and optimization
├── code-examples.md           # Practical examples and recipes
├── testing-best-practices.md  # Testing and production guidance
├── migration-guide.md         # Version upgrade guide
├── error-handling.md          # Error handling strategies
├── http-client-factory.md     # HTTP client configuration
├── logging.md                 # Logging setup and configuration
└── docs/
    ├── getting-started.md     # Comprehensive getting started guide
    ├── introduction.md        # Library introduction
    ├── obtaining-an-api-key.md # API key setup
    └── downloading-a-photo.md # Photo download examples
</code></pre>
<h3 id="content-organization">Content Organization</h3>
<h4 id="by-complexity-level">By Complexity Level</h4>
<ul>
<li><strong>Beginner</strong>: <code>docs/getting-started.md</code>, <code>docs/obtaining-an-api-key.md</code></li>
<li><strong>Intermediate</strong>: <code>api-reference.md</code>, <code>models-reference.md</code>, <code>error-handling.md</code></li>
<li><strong>Advanced</strong>: <code>advanced-usage.md</code>, <code>testing-best-practices.md</code>, <code>migration-guide.md</code></li>
</ul>
<h4 id="by-topic">By Topic</h4>
<ul>
<li><strong>Setup &amp; Configuration</strong>: Getting started, API keys, HTTP client factory, logging</li>
<li><strong>API Usage</strong>: API reference, models, code examples</li>
<li><strong>Advanced Topics</strong>: Advanced usage, performance, batch operations</li>
<li><strong>Quality &amp; Production</strong>: Testing, best practices, error handling, monitoring</li>
<li><strong>Migration &amp; Maintenance</strong>: Migration guide, troubleshooting</li>
</ul>
<h2 id="-search-tips">🔍 Search Tips</h2>
<h3 id="finding-specific-information">Finding Specific Information</h3>
<ul>
<li><strong>Method documentation</strong>: Check <a href="api-reference.html">API Reference</a></li>
<li><strong>Data structures</strong>: See <a href="models-reference.html">Model Reference</a></li>
<li><strong>Code examples</strong>: Browse <a href="code-examples.html">Code Examples</a></li>
<li><strong>Error solutions</strong>: Search <a href="error-handling.html">Error Handling</a></li>
<li><strong>Performance tips</strong>: Look in <a href="advanced-usage.html">Advanced Usage</a></li>
</ul>
<h3 id="using-documentation-search">Using Documentation Search</h3>
<ol>
<li>Use browser search (Ctrl+F / Cmd+F) within pages</li>
<li>Search for specific method names (e.g., &quot;GetRandomPhoto&quot;)</li>
<li>Look for error types (e.g., &quot;UnsplasharpRateLimitException&quot;)</li>
<li>Search by use case (e.g., &quot;download&quot;, &quot;search&quot;, &quot;cache&quot;)</li>
</ol>
<h2 id="-learning-paths">🚀 Learning Paths</h2>
<h3 id="path-1-quick-start-30-minutes">Path 1: Quick Start (30 minutes)</h3>
<ol>
<li><a href="docs/getting-started.html">Getting Started</a> - Setup and first request</li>
<li><a href="code-examples.html#basic-operations">Basic Examples</a> - Simple operations</li>
<li><a href="error-handling.html#basic-error-handling">Error Handling Basics</a> - Handle errors</li>
</ol>
<h3 id="path-2-building-an-app-2-3-hours">Path 2: Building an App (2-3 hours)</h3>
<ol>
<li><a href="docs/getting-started.html">Getting Started</a> - Foundation</li>
<li><a href="api-reference.html">API Reference</a> - Learn the API</li>
<li><a href="code-examples.html">Code Examples</a> - Implementation patterns</li>
<li><a href="error-handling.html">Error Handling</a> - Robust error handling</li>
<li><a href="testing-best-practices.html">Testing</a> - Quality assurance</li>
</ol>
<h3 id="path-3-production-ready-1-2-days">Path 3: Production Ready (1-2 days)</h3>
<ol>
<li>Complete Path 2 above</li>
<li><a href="advanced-usage.html">Advanced Usage</a> - Optimization techniques</li>
<li><a href="testing-best-practices.html#best-practices">Best Practices</a> - Production guidelines</li>
<li><a href="testing-best-practices.html#security-considerations">Security</a> - Secure implementation</li>
<li><a href="testing-best-practices.html#monitoring-and-observability">Monitoring</a> - Observability</li>
</ol>
<h3 id="path-4-expert-level-ongoing">Path 4: Expert Level (Ongoing)</h3>
<ol>
<li>Complete Path 3 above</li>
<li><a href="migration-guide.html">Migration Guide</a> - Stay current</li>
<li><a href="advanced-usage.html">Advanced Patterns</a> - Complex scenarios</li>
<li><a href="advanced-usage.html#performance-optimization">Performance Optimization</a> - Maximum efficiency</li>
<li>Contribute to documentation and examples</li>
</ol>
<h2 id="-mobile-friendly-navigation">📱 Mobile-Friendly Navigation</h2>
<h3 id="quick-links">Quick Links</h3>
<ul>
<li><a href="docs/getting-started.html">📖 Getting Started</a></li>
<li><a href="api-reference.html">🔍 API Reference</a></li>
<li><a href="code-examples.html">💡 Examples</a></li>
<li><a href="error-handling.html">⚠️ Error Handling</a></li>
<li><a href="advanced-usage.html">🚀 Advanced</a></li>
<li><a href="testing-best-practices.html">🧪 Testing</a></li>
</ul>
<h3 id="bookmarks-for-development">Bookmarks for Development</h3>
<p>Save these for quick reference during development:</p>
<ul>
<li><a href="api-reference.html#table-of-contents">Method List</a></li>
<li><a href="error-handling.html#exception-types">Error Types</a></li>
<li><a href="models-reference.html#photo-model">Model Properties</a></li>
<li><a href="code-examples.html#basic-operations">Code Snippets</a></li>
</ul>
<hr>
<p><strong>Need help finding something?</strong> Check the <a href="index.html">main documentation index</a> or <a href="https://github.com/rootasjey/unsplasharp/issues">open an issue</a> on GitHub.</p>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/rootasjey/unsplasharp/blob/master/docs/navigation.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
